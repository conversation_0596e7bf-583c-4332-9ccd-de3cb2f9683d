<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 -4.76837158203125e-7 2057.607177734375 549.49853515625" style="max-width: 2057.607177734375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535"><style>#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .error-icon{fill:#a44141;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edge-thickness-normal{stroke-width:1px;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .marker.cross{stroke:lightgrey;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 p{margin:0;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .cluster-label text{fill:#F9FFFE;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .cluster-label span{color:#F9FFFE;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .cluster-label span p{background-color:transparent;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .label text,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 span{fill:#ccc;color:#ccc;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .node rect,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .node circle,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .node ellipse,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .node polygon,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .rough-node .label text,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .node .label text,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .image-shape .label,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .icon-shape .label{text-anchor:middle;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .rough-node .label,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .node .label,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .image-shape .label,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .icon-shape .label{text-align:center;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .node.clickable{cursor:pointer;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .arrowheadPath{fill:lightgrey;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .cluster text{fill:#F9FFFE;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .cluster span{color:#F9FFFE;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 rect.text{fill:none;stroke-width:0;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .icon-shape,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .icon-shape p,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .icon-shape rect,#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .primary&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:3px!important;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .primary span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:3px!important;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .data&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .data span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .processing&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .processing span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .monitoring&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .monitoring span{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .structure&gt;*{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535 .structure span{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CSV_KAFKA_0" d="M411.493,272.019L471.294,299.401C531.095,326.784,650.698,381.549,722.066,413.34C793.434,445.132,816.568,453.949,828.135,458.358L839.702,462.767"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SENSORS_KAFKA_1" d="M419.159,500.374L477.682,500.374C536.206,500.374,653.253,500.374,723.301,499.561C793.35,498.749,816.399,497.124,827.924,496.311L839.449,495.499"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_KAFKA_STREAM_2" d="M980.829,490.374L986.927,490.374C993.026,490.374,1005.224,490.374,1014.823,490.374C1024.421,490.374,1031.421,490.374,1034.921,490.374L1038.421,490.374"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CSV_ML_3" d="M411.493,246.85L471.294,245.896C531.095,244.941,650.698,243.033,720.091,242.523C789.484,242.014,808.667,242.903,818.259,243.347L827.851,243.792"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ML_MODELS_4" d="M992.421,247.697L996.588,247.697C1000.755,247.697,1009.088,247.697,1024.299,247.697C1039.511,247.697,1061.6,247.697,1072.645,247.697L1083.689,247.697"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MODELS_FLASK_5" d="M1213.336,247.697L1231.412,247.697C1249.489,247.697,1285.642,247.697,1317.034,259.808C1348.427,271.918,1375.059,296.139,1388.375,308.25L1401.691,320.36"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_STREAM_FLASK_6" d="M1258.604,490.374L1269.135,490.374C1279.667,490.374,1300.731,490.374,1323.758,479.91C1346.785,469.445,1371.775,448.517,1384.27,438.052L1396.765,427.588"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FLASK_PROMETHEUS_7" d="M1536.432,337.156L1545.709,332.638C1554.987,328.12,1573.542,319.083,1594.542,314.565C1615.543,310.046,1638.989,310.046,1650.712,310.046L1662.436,310.046"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PROMETHEUS_GRAFANA_8" d="M1837.232,310.046L1844.511,310.046C1851.79,310.046,1866.349,310.046,1877.128,310.046C1887.907,310.046,1894.907,310.046,1898.407,310.046L1901.907,310.046"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FLASK_FRONTEND_9" d="M1536.432,410.915L1545.709,415.433C1554.987,419.952,1573.542,428.988,1591.43,433.507C1609.318,438.025,1626.539,438.025,1635.15,438.025L1643.761,438.025"></path><path marker-end="url(#mermaid-3dd96780-c3bf-4559-8bd9-b6ae0a548535_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AIRFLOW_ML_10" d="M426.996,372.311L484.214,371.398C541.431,370.485,655.865,368.659,728.475,354.817C801.084,340.975,831.869,315.117,847.261,302.188L862.653,289.259"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1321.7946014404297, 490.37392807006836)" class="edgeLabel"><g transform="translate(-38.191070556640625, -11.99464225769043)" class="label"><foreignObject height="23.98928451538086" width="76.38214111328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP Calls</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1592.0963821411133, 438.0249500274658)" class="edgeLabel"><g transform="translate(-30.664283752441406, -11.99464225769043)" class="label"><foreignObject height="23.98928451538086" width="61.32856750488281"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>REST API</p></span></div></foreignObject></g></g><g transform="translate(770.2999649047852, 366.8327808380127)" class="edgeLabel"><g transform="translate(-36.546424865722656, -11.99464225769043)" class="label"><foreignObject height="23.98928451538086" width="73.09284973144531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Scheduled</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(0, 0)" class="root"><g class="clusters"><g data-look="classic" id="subGraph0" class="cluster"><rect height="152.97856903076172" width="700.7535552978516" y="8" x="8" style=""></rect><g transform="translate(273.1178512573242, 8)" class="cluster-label"><foreignObject height="23.98928451538086" width="170.51785278320312"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📁 Organized Structure</p></span></div></foreignObject></g></g></g><g class="edgePaths"></g><g class="edgeLabels"></g><g class="nodes"><g transform="translate(119.46785736083984, 84.48928451538086)" id="flowchart-DOCKER-4272" class="node default structure"><rect height="77.97856903076172" width="152.93570709228516" y="-38.98928451538086" x="-76.46785354614258" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.46785354614258, -23.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="92.93570709228516"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🐳 docker/<br>8 Dockerfiles</p></span></div></foreignObject></g></g><g transform="translate(330.3267822265625, 84.48928451538086)" id="flowchart-DOCS-4273" class="node default structure"><rect height="77.97856903076172" width="168.78213500976562" y="-38.98928451538086" x="-84.39106750488281" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-54.39106750488281, -23.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="108.78213500976562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📚 docs/<br>Documentation</p></span></div></foreignObject></g></g><g transform="translate(569.2357025146484, 84.48928451538086)" id="flowchart-REQ-4274" class="node default structure"><rect height="77.97856903076172" width="209.03570556640625" y="-38.98928451538086" x="-104.51785278320312" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-74.51785278320312, -23.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="149.03570556640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📋 requirements.txt<br>Unified</p></span></div></foreignObject></g></g></g></g><g transform="translate(358.37677001953125, 247.69740295410156)" id="flowchart-CSV-4239" class="node default data"><path transform="translate(-53.11606979370117, -36.722806308050764)" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container" d="M0,11.485442700240224 a53.11606979370117,11.485442700240224 0,0,0 106.23213958740234,0 a53.11606979370117,11.485442700240224 0,0,0 -106.23213958740234,0 l0,50.47472721562109 a53.11606979370117,11.485442700240224 0,0,0 106.23213958740234,0 l0,-50.47472721562109"></path><g transform="translate(-45.61606979370117, -1.9946422576904297)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="91.23213958740234"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 CSV Data</p></span></div></foreignObject></g></g><g transform="translate(358.37677001953125, 500.37392807006836)" id="flowchart-SENSORS-4240" class="node default data"><path transform="translate(-60.78213882446289, -37.9833724796962)" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container" d="M0,12.32582014800385 a60.78213882446289,12.32582014800385 0,0,0 121.56427764892578,0 a60.78213882446289,12.32582014800385 0,0,0 -121.56427764892578,0 l0,51.31510466338471 a60.78213882446289,12.32582014800385 0,0,0 121.56427764892578,0 l0,-51.31510466338471"></path><g transform="translate(-53.28213882446289, -1.9946422576904297)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="106.56427764892578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌡️ IoT Sensors</p></span></div></foreignObject></g></g><g transform="translate(912.1338882446289, 490.37392807006836)" id="flowchart-KAFKA-4241" class="node default data"><path transform="translate(-68.69464111328125, -51.12460665878863)" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container" d="M0,13.090214762271847 a68.69464111328125,13.090214762271847 0,0,0 137.3892822265625,0 a68.69464111328125,13.090214762271847 0,0,0 -137.3892822265625,0 l0,76.06878379303356 a68.69464111328125,13.090214762271847 0,0,0 137.3892822265625,0 l0,-76.06878379303356"></path><g transform="translate(-61.19464111328125, -13.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="122.3892822265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔄 Kafka<br>smoke_detection</p></span></div></foreignObject></g></g><g transform="translate(1150.5124588012695, 490.37392807006836)" id="flowchart-STREAM-4242" class="node default processing"><rect height="77.97856903076172" width="216.18212890625" y="-38.98928451538086" x="-108.091064453125" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-78.091064453125, -23.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="156.18212890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚡ Stream Processing<br>Real-time Analytics</p></span></div></foreignObject></g></g><g transform="translate(1460.708885192871, 374.03566551208496)" id="flowchart-FLASK-4243" class="node default primary"><rect height="101.96784973144531" width="151.44642639160156" y="-50.983924865722656" x="-75.72321319580078" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-45.72321319580078, -35.983924865722656)" style="" class="label"><rect></rect><foreignObject height="71.96784973144531" width="91.44642639160156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔥 Flask API<br>12 Endpoints<br>Port: 5000</p></span></div></foreignObject></g></g><g transform="translate(912.1338882446289, 247.69740295410156)" id="flowchart-ML-4244" class="node default processing"><rect height="77.97856903076172" width="160.5749969482422" y="-38.98928451538086" x="-80.2874984741211" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-50.287498474121094, -23.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="100.57499694824219"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🤖 ML Trainer<br>Auto-training</p></span></div></foreignObject></g></g><g transform="translate(1150.5124588012695, 247.69740295410156)" id="flowchart-MODELS-4245" class="node default data"><path transform="translate(-62.823211669921875, -50.287641230232666)" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container" d="M0,12.532237809901204 a62.823211669921875,12.532237809901204 0,0,0 125.64642333984375,0 a62.823211669921875,12.532237809901204 0,0,0 -125.64642333984375,0 l0,75.51080684066292 a62.823211669921875,12.532237809901204 0,0,0 125.64642333984375,0 l0,-75.51080684066292"></path><g transform="translate(-55.323211669921875, -13.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="110.64642333984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🗃️ Models<br>best_model.pkl</p></span></div></foreignObject></g></g><g transform="translate(1751.8338775634766, 310.0463809967041)" id="flowchart-PROMETHEUS-4246" class="node default monitoring"><rect height="77.97856903076172" width="170.79642486572266" y="-38.98928451538086" x="-85.39821243286133" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-55.39821243286133, -23.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="110.79642486572266"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📈 Prometheus<br>Port: 9090</p></span></div></foreignObject></g></g><g transform="translate(1977.7570877075195, 310.0463809967041)" id="flowchart-GRAFANA-4247" class="node default monitoring"><rect height="77.97856903076172" width="143.6999969482422" y="-38.98928451538086" x="-71.8499984741211" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-41.849998474121094, -23.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="83.69999694824219"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📊 Grafana<br>Port: 3000</p></span></div></foreignObject></g></g><g transform="translate(1751.8338775634766, 438.0249500274658)" id="flowchart-FRONTEND-4248" class="node default"><rect height="77.97856903076172" width="208.14642333984375" y="-38.98928451538086" x="-104.07321166992188" style="" class="basic label-container"></rect><g transform="translate(-74.07321166992188, -23.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="148.14642333984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🎨 Frontend Apps<br>JavaScript API Client</p></span></div></foreignObject></g></g><g transform="translate(358.37677001953125, 373.4055290222168)" id="flowchart-AIRFLOW-4249" class="node default processing"><rect height="77.97856903076172" width="137.2392807006836" y="-38.98928451538086" x="-68.6196403503418" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-38.6196403503418, -23.98928451538086)" style="" class="label"><rect></rect><foreignObject height="47.97856903076172" width="77.2392807006836"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌬️ Airflow<br>Port: 8080</p></span></div></foreignObject></g></g></g></g></g></svg>