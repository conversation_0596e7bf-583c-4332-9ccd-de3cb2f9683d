# 🎨 Frontend Developer Guide - IoT Smoke Detection Dashboard

## 📋 **Overview**

This guide provides comprehensive documentation for frontend developers to build interactive dashboards and user interfaces for the IoT Smoke Detection Data Pipeline system.

## 🏗️ **System Architecture for Frontend**

### **Backend Services Available**
| Service | URL | Purpose | Status |
|---------|-----|---------|--------|
| **Flask API** | `http://localhost:5000` | Main prediction API | ✅ Ready |
| **Spark UI** | `http://localhost:4040` | Stream processing monitoring | ✅ Ready |
| **Airflow UI** | `http://localhost:8080` | Workflow management | ✅ Ready |
| **Prometheus** | `http://localhost:9090` | Metrics collection | ✅ Ready |
| **Grafana** | `http://localhost:3000` | Data visualization dashboards | ✅ Ready |

### **Primary Integration Point**
**Flask API Backend**: `http://localhost:5000`
- **Purpose**: Central API for all frontend interactions
- **Authentication**: None (can be added later)
- **Response Format**: JSON
- **CORS**: Enabled for all origins

## 🔌 **API Endpoints Reference**

### **🔥 Prediction Endpoints**

#### **1. Single Prediction**
```http
POST /predict
Content-Type: application/json

{
  "Temperature[C]": 25.5,
  "Humidity[%]": 45.0,
  "TVOC[ppb]": 150.0,
  "eCO2[ppm]": 400.0,
  "Raw H2": 13000.0,
  "Raw Ethanol": 18500.0,
  "Pressure[hPa]": 1013.25,
  "PM1.0": 10.0,
  "PM2.5": 15.0,
  "NC0.5": 100.0,
  "NC1.0": 80.0,
  "NC2.5": 20.0
}
```

**Response:**
```json
{
  "prediction": 0,
  "prediction_label": "no_fire",
  "confidence": {
    "no_fire": 0.85,
    "fire": 0.15
  },
  "processing_time_seconds": 0.023,
  "timestamp": "2024-01-15T10:30:00.123Z",
  "model_info": {
    "algorithm": "RandomForestClassifier",
    "version": "1.0"
  }
}
```

#### **2. Batch Predictions**
```http
POST /predict/batch
Content-Type: application/json

[
  {
    "Temperature[C]": 25.5,
    "Humidity[%]": 45.0,
    // ... other sensor fields
  },
  {
    "Temperature[C]": 85.0,
    "Humidity[%]": 20.0,
    // ... other sensor fields
  }
]
```

**Response:**
```json
{
  "predictions": [
    {
      "index": 0,
      "prediction": 0,
      "prediction_label": "no_fire",
      "confidence": {"no_fire": 0.85, "fire": 0.15}
    },
    {
      "index": 1,
      "prediction": 1,
      "prediction_label": "fire",
      "confidence": {"no_fire": 0.25, "fire": 0.75}
    }
  ],
  "summary": {
    "total_samples": 2,
    "successful_predictions": 2,
    "fire_detections": 1,
    "no_fire_detections": 1,
    "errors": 0
  },
  "processing_time_seconds": 0.045,
  "timestamp": "2024-01-15T10:30:00.123Z"
}
```

#### **3. Sample Data Test**
```http
GET /predict/sample
```

**Response:** Returns prediction using built-in sample data

#### **4. Fire Scenario Test**
```http
GET /predict/fire-scenario
```

**Response:** Returns prediction using fire condition data

### **🔍 System Information Endpoints**

#### **5. Health Check**
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.123Z",
  "model_loaded": true,
  "model_path": "ml/models/best_model.pkl",
  "uptime_seconds": 3600
}
```

#### **6. Model Information**
```http
GET /model/info
```

**Response:**
```json
{
  "model_type": "RandomForestClassifier",
  "algorithm": "RandomForestClassifier",
  "feature_count": 12,
  "training_timestamp": "2024-01-15T09:00:00.000Z",
  "version": "1.0",
  "loaded_at": "2024-01-15T10:00:00.123Z"
}
```

#### **7. Data Validation**
```http
POST /validate
Content-Type: application/json

{
  "Temperature[C]": 25.5,
  "Humidity[%]": 45.0
  // ... sensor data to validate
}
```

**Response:**
```json
{
  "valid": true,
  "errors": [],
  "warnings": []
}
```

#### **8. Prometheus Metrics**
```http
GET /metrics
```

**Response:** Prometheus-formatted metrics for monitoring

## 📊 **Required Sensor Fields**

### **Input Data Schema**
All prediction endpoints require these sensor fields:

| Field | Type | Unit | Range | Description |
|-------|------|------|-------|-------------|
| `Temperature[C]` | float | Celsius | -40 to 125 | Ambient temperature |
| `Humidity[%]` | float | Percentage | 0 to 100 | Relative humidity |
| `TVOC[ppb]` | float | ppb | 0 to 60000 | Total Volatile Organic Compounds |
| `eCO2[ppm]` | float | ppm | 400 to 8192 | Equivalent CO2 |
| `Raw H2` | float | Raw value | 0 to 65535 | Hydrogen sensor reading |
| `Raw Ethanol` | float | Raw value | 0 to 65535 | Ethanol sensor reading |
| `Pressure[hPa]` | float | hPa | 300 to 1100 | Atmospheric pressure |
| `PM1.0` | float | μg/m³ | 0 to 1000 | Particulate Matter 1.0 |
| `PM2.5` | float | μg/m³ | 0 to 1000 | Particulate Matter 2.5 |
| `NC0.5` | float | #/cm³ | 0 to 10000 | Number Concentration 0.5μm |
| `NC1.0` | float | #/cm³ | 0 to 10000 | Number Concentration 1.0μm |
| `NC2.5` | float | #/cm³ | 0 to 10000 | Number Concentration 2.5μm |

### **Validation Rules**
- All fields are **required**
- All values must be **numeric** (int or float)
- Values should be within **specified ranges**
- Missing fields will cause validation errors

## 🎨 **Frontend Implementation Examples**

### **JavaScript/React Example**

#### **1. Basic Prediction Function**
```javascript
// API client for smoke detection
class SmokeDetectionAPI {
  constructor(baseURL = 'http://localhost:5000') {
    this.baseURL = baseURL;
  }

  async predict(sensorData) {
    try {
      const response = await fetch(`${this.baseURL}/predict`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sensorData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Prediction error:', error);
      throw error;
    }
  }

  async batchPredict(sensorDataArray) {
    try {
      const response = await fetch(`${this.baseURL}/predict/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sensorDataArray)
      });

      return await response.json();
    } catch (error) {
      console.error('Batch prediction error:', error);
      throw error;
    }
  }

  async getHealth() {
    const response = await fetch(`${this.baseURL}/health`);
    return await response.json();
  }

  async getModelInfo() {
    const response = await fetch(`${this.baseURL}/model/info`);
    return await response.json();
  }

  async validateData(sensorData) {
    const response = await fetch(`${this.baseURL}/validate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sensorData)
    });
    return await response.json();
  }
}

// Usage example
const api = new SmokeDetectionAPI();

// Example sensor data
const sensorData = {
  'Temperature[C]': 25.5,
  'Humidity[%]': 45.0,
  'TVOC[ppb]': 150.0,
  'eCO2[ppm]': 400.0,
  'Raw H2': 13000.0,
  'Raw Ethanol': 18500.0,
  'Pressure[hPa]': 1013.25,
  'PM1.0': 10.0,
  'PM2.5': 15.0,
  'NC0.5': 100.0,
  'NC1.0': 80.0,
  'NC2.5': 20.0
};

// Make prediction
api.predict(sensorData)
  .then(result => {
    console.log('Prediction:', result.prediction_label);
    console.log('Confidence:', result.confidence);
  })
  .catch(error => {
    console.error('Error:', error);
  });
```

#### **2. React Component Example**
```jsx
import React, { useState, useEffect } from 'react';

const SmokeDetectionDashboard = () => {
  const [sensorData, setSensorData] = useState({
    'Temperature[C]': 25.0,
    'Humidity[%]': 50.0,
    'TVOC[ppb]': 100.0,
    'eCO2[ppm]': 400.0,
    'Raw H2': 13000.0,
    'Raw Ethanol': 18500.0,
    'Pressure[hPa]': 1013.25,
    'PM1.0': 10.0,
    'PM2.5': 15.0,
    'NC0.5': 100.0,
    'NC1.0': 80.0,
    'NC2.5': 20.0
  });

  const [prediction, setPrediction] = useState(null);
  const [loading, setLoading] = useState(false);
  const [systemHealth, setSystemHealth] = useState(null);

  const api = new SmokeDetectionAPI();

  // Check system health on component mount
  useEffect(() => {
    checkSystemHealth();
  }, []);

  const checkSystemHealth = async () => {
    try {
      const health = await api.getHealth();
      setSystemHealth(health);
    } catch (error) {
      console.error('Health check failed:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setSensorData(prev => ({
      ...prev,
      [field]: parseFloat(value) || 0
    }));
  };

  const makePrediction = async () => {
    setLoading(true);
    try {
      const result = await api.predict(sensorData);
      setPrediction(result);
    } catch (error) {
      console.error('Prediction failed:', error);
      setPrediction({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testFireScenario = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:5000/predict/fire-scenario');
      const result = await response.json();
      setPrediction(result.prediction);
      setSensorData(result.input_data);
    } catch (error) {
      console.error('Fire scenario test failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="dashboard">
      <h1>🔥 IoT Smoke Detection Dashboard</h1>

      {/* System Status */}
      <div className="system-status">
        <h2>System Status</h2>
        {systemHealth && (
          <div className={`status ${systemHealth.status}`}>
            <span>Status: {systemHealth.status}</span>
            <span>Model Loaded: {systemHealth.model_loaded ? '✅' : '❌'}</span>
            <span>Uptime: {Math.floor(systemHealth.uptime_seconds / 60)} minutes</span>
          </div>
        )}
      </div>

      {/* Sensor Input Form */}
      <div className="sensor-inputs">
        <h2>Sensor Readings</h2>
        <div className="input-grid">
          {Object.entries(sensorData).map(([field, value]) => (
            <div key={field} className="input-group">
              <label>{field}</label>
              <input
                type="number"
                step="0.1"
                value={value}
                onChange={(e) => handleInputChange(field, e.target.value)}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="actions">
        <button
          onClick={makePrediction}
          disabled={loading}
          className="predict-btn"
        >
          {loading ? 'Analyzing...' : 'Analyze Fire Risk'}
        </button>

        <button
          onClick={testFireScenario}
          disabled={loading}
          className="test-btn"
        >
          Test Fire Scenario
        </button>
      </div>

      {/* Prediction Results */}
      {prediction && (
        <div className="prediction-results">
          <h2>Analysis Results</h2>
          {prediction.error ? (
            <div className="error">Error: {prediction.error}</div>
          ) : (
            <div className={`result ${prediction.prediction_label}`}>
              <div className="prediction-label">
                {prediction.prediction_label === 'fire' ? '🔥 FIRE DETECTED' : '✅ NO FIRE'}
              </div>

              {prediction.confidence && (
                <div className="confidence">
                  <div>Fire Risk: {(prediction.confidence.fire * 100).toFixed(1)}%</div>
                  <div>Safe: {(prediction.confidence.no_fire * 100).toFixed(1)}%</div>
                </div>
              )}

              <div className="metadata">
                <small>
                  Processing Time: {(prediction.processing_time_seconds * 1000).toFixed(1)}ms |
                  Model: {prediction.model_info?.algorithm} v{prediction.model_info?.version}
                </small>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SmokeDetectionDashboard;
```

#### **3. CSS Styling Example**
```css
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.system-status {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.status {
  display: flex;
  gap: 20px;
  align-items: center;
}

.status.healthy {
  color: #28a745;
}

.status.degraded {
  color: #ffc107;
}

.input-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.input-group {
  display: flex;
  flex-direction: column;
}

.input-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #495057;
}

.input-group input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.actions {
  display: flex;
  gap: 15px;
  margin: 20px 0;
}

.predict-btn, .test-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.predict-btn {
  background: #007bff;
  color: white;
}

.predict-btn:hover {
  background: #0056b3;
}

.test-btn {
  background: #6c757d;
  color: white;
}

.test-btn:hover {
  background: #545b62;
}

.predict-btn:disabled, .test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.prediction-results {
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.result.fire {
  border-left: 4px solid #dc3545;
}

.result.no_fire {
  border-left: 4px solid #28a745;
}

.prediction-label {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 15px;
}

.result.fire .prediction-label {
  color: #dc3545;
}

.result.no_fire .prediction-label {
  color: #28a745;
}

.confidence {
  display: flex;
  gap: 20px;
  margin: 15px 0;
  font-size: 18px;
}

.metadata {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #dee2e6;
  color: #6c757d;
}

.error {
  color: #dc3545;
  background: #f8d7da;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}
```

## 📱 **Mobile-First Responsive Design**

### **Responsive CSS Additions**
```css
/* Mobile-first responsive design */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }

  .input-grid {
    grid-template-columns: 1fr;
  }

  .actions {
    flex-direction: column;
  }

  .predict-btn, .test-btn {
    width: 100%;
  }

  .confidence {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .prediction-label {
    font-size: 20px;
  }

  .confidence {
    font-size: 16px;
  }
}
```

## 🔄 **Real-time Updates**

### **WebSocket Integration (Future Enhancement)**
```javascript
// Real-time sensor data updates
class RealTimeSensorData {
  constructor(wsURL = 'ws://localhost:8080/ws') {
    this.wsURL = wsURL;
    this.callbacks = [];
  }

  connect() {
    this.ws = new WebSocket(this.wsURL);

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.callbacks.forEach(callback => callback(data));
    };

    this.ws.onopen = () => {
      console.log('Connected to real-time sensor data');
    };

    this.ws.onclose = () => {
      console.log('Disconnected from sensor data');
      // Reconnect after 5 seconds
      setTimeout(() => this.connect(), 5000);
    };
  }

  onSensorData(callback) {
    this.callbacks.push(callback);
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

// Usage in React component
useEffect(() => {
  const realTimeData = new RealTimeSensorData();

  realTimeData.onSensorData((data) => {
    setSensorData(data);
    // Automatically make prediction on new data
    api.predict(data).then(setPrediction);
  });

  realTimeData.connect();

  return () => realTimeData.disconnect();
}, []);
```

## 📊 **Data Visualization Examples**

### **Chart.js Integration**
```javascript
import { Line, Bar, Doughnut } from 'react-chartjs-2';

// Sensor readings over time
const SensorChart = ({ sensorHistory }) => {
  const chartData = {
    labels: sensorHistory.map(d => new Date(d.timestamp).toLocaleTimeString()),
    datasets: [
      {
        label: 'Temperature (°C)',
        data: sensorHistory.map(d => d['Temperature[C]']),
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
      },
      {
        label: 'Humidity (%)',
        data: sensorHistory.map(d => d['Humidity[%]']),
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
      }
    ]
  };

  return <Line data={chartData} />;
};

// Fire risk gauge
const FireRiskGauge = ({ confidence }) => {
  const fireRisk = confidence?.fire || 0;

  const gaugeData = {
    labels: ['Safe', 'Low Risk', 'High Risk', 'Fire'],
    datasets: [{
      data: [
        Math.max(0, 0.25 - fireRisk),
        Math.max(0, Math.min(0.25, fireRisk)),
        Math.max(0, Math.min(0.25, fireRisk - 0.25)),
        Math.max(0, fireRisk - 0.5)
      ],
      backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'],
    }]
  };

  return <Doughnut data={gaugeData} />;
};
```

## 🚀 **Deployment Considerations**

### **Environment Configuration**
```javascript
// config.js
const config = {
  development: {
    API_BASE_URL: 'http://localhost:5000',
    WS_URL: 'ws://localhost:8080/ws',
    POLLING_INTERVAL: 5000
  },
  production: {
    API_BASE_URL: 'https://your-domain.com/api',
    WS_URL: 'wss://your-domain.com/ws',
    POLLING_INTERVAL: 10000
  }
};

export default config[process.env.NODE_ENV || 'development'];
```

### **Error Handling Best Practices**
```javascript
// Error boundary component
class APIErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('API Error:', error, errorInfo);
    // Log to monitoring service
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>🚨 System Error</h2>
          <p>The smoke detection system encountered an error.</p>
          <button onClick={() => window.location.reload()}>
            Reload Dashboard
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## 🔧 **Testing the API**

### **Quick API Test Commands**
```bash
# Test health endpoint
curl http://localhost:5000/health

# Test sample prediction
curl http://localhost:5000/predict/sample

# Test fire scenario
curl http://localhost:5000/predict/fire-scenario

# Test custom prediction
curl -X POST http://localhost:5000/predict \
  -H "Content-Type: application/json" \
  -d '{
    "Temperature[C]": 25.5,
    "Humidity[%]": 45.0,
    "TVOC[ppb]": 150.0,
    "eCO2[ppm]": 400.0,
    "Raw H2": 13000.0,
    "Raw Ethanol": 18500.0,
    "Pressure[hPa]": 1013.25,
    "PM1.0": 10.0,
    "PM2.5": 15.0,
    "NC0.5": 100.0,
    "NC1.0": 80.0,
    "NC2.5": 20.0
  }'
```

## 📚 **Recommended Frontend Stack**

### **Modern React Stack**
- **React 18+** - Component framework
- **TypeScript** - Type safety
- **Vite** - Fast build tool
- **Tailwind CSS** - Utility-first CSS
- **React Query** - API state management
- **Chart.js/D3.js** - Data visualization
- **React Hook Form** - Form handling

### **Alternative Frameworks**
- **Vue.js 3** with Composition API
- **Angular 15+** with standalone components
- **Svelte/SvelteKit** for lightweight apps
- **Next.js** for full-stack React apps

## 🎯 **Next Steps**

1. **Start with the basic React component** provided above
2. **Test API endpoints** using the curl commands
3. **Implement real-time updates** using polling or WebSockets
4. **Add data visualization** with charts and gauges
5. **Enhance UX** with loading states and error handling
6. **Deploy** using your preferred hosting platform

## 📞 **Support & Resources**

- **API Documentation**: This guide
- **Backend Health**: `GET /health`
- **System Monitoring**: Spark UI at `http://localhost:4040`
- **Workflow Status**: Airflow UI at `http://localhost:8080`

**Happy coding! 🚀 Build amazing smoke detection dashboards!**
```
