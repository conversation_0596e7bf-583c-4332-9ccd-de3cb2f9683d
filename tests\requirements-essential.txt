# Essential Test Dependencies for IoT Smoke Detection Data Pipeline
# Install with: pip install -r tests/requirements-essential.txt

# Core testing framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-html>=3.2.0
pytest-timeout>=2.1.0
pytest-mock>=3.11.1

# Streaming and API testing
kafka-python>=2.0.2
requests>=2.31.0

# Data science dependencies (already in main requirements)
# pandas>=2.0.0
# numpy>=1.24.0
# scikit-learn>=1.3.0

# Environment and configuration
python-dotenv>=1.0.0

# Performance monitoring
psutil>=5.9.5

# Mock and test utilities
responses>=0.23.3
freezegun>=1.2.2

# ML testing utilities (if not already installed)
# numpy>=1.24.0
# pandas>=2.0.0
# scikit-learn>=1.3.0

# Coverage reporting
coverage[toml]>=7.2.0
