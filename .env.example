# .env.example - template file for environment variables
# Please copy this file to ".env" and fill in your actual values

# ---------------- zookeeper Config ----------------
ZOOKEEPER_CLIENT_PORT=2181

# ---------------- Ka<PERSON>ka Config ----------------
KAFKA_PORT=9092
KAFKA_BROKER_ID=1
KAFKA_TOPIC_SMOKE=smoke_sensor_data

# ---------------- Flask API ----------------
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# ---------------- Airflow ----------------
AIRFLOW_UID=50000
AIRFLOW_GID=0
AIRFLOW__CORE__EXECUTOR=LocalExecutor
AIRFLOW__CORE__FERNET_KEY=change_this_fernet_key
AIRFLOW__WEBSERVER__DEFAULT_USER=admin
AIRFLOW__WEBSERVER__DEFAULT_PASSWORD=change_this_password

# ---------------- Monitoring ----------------
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# ---------------- Postgresql ----------------
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password
POSTGRES_DB=your_DB
POSTGRES_PORT=5432