<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 -4.76837158203125e-7 3485.106201171875 1405.892822265625" style="max-width: 3485.106201171875px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877"><style>#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .error-icon{fill:#a44141;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edge-thickness-normal{stroke-width:1px;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .marker.cross{stroke:lightgrey;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 p{margin:0;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .cluster-label text{fill:#F9FFFE;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .cluster-label span{color:#F9FFFE;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .cluster-label span p{background-color:transparent;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .label text,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 span{fill:#ccc;color:#ccc;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .node rect,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .node circle,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .node ellipse,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .node polygon,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .rough-node .label text,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .node .label text,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .image-shape .label,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .icon-shape .label{text-anchor:middle;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .rough-node .label,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .node .label,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .image-shape .label,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .icon-shape .label{text-align:center;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .node.clickable{cursor:pointer;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .arrowheadPath{fill:lightgrey;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .cluster text{fill:#F9FFFE;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .cluster span{color:#F9FFFE;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 rect.text{fill:none;stroke-width:0;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .icon-shape,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .icon-shape p,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .icon-shape rect,#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph8" class="cluster"><rect height="103.98928451538086" width="1488.516918182373" y="1010.924991607666" x="1445.3329887390137" style=""></rect><g transform="translate(2142.5825214385986, 1010.924991607666)" class="cluster-label"><foreignObject height="23.98928451538086" width="94.01785278320312"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Output Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph7" class="cluster"><rect height="232.97856903076172" width="2301.4927978515625" y="1164.9142761230469" x="1068.1329879760742" style=""></rect><g transform="translate(2141.0615310668945, 1164.9142761230469)" class="cluster-label"><foreignObject height="23.98928451538086" width="155.63571166992188"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Monitoring &amp; UI Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="103.98928451538086" width="841.1606903076172" y="856.9357070922852" x="1066.033878326416" style=""></rect><g transform="translate(1437.5320854187012, 856.9357070922852)" class="cluster-label"><foreignObject height="23.98928451538086" width="98.16427612304688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Storage Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="257.9785690307617" width="2091.9240226745605" y="548.9571380615234" x="8" style=""></rect><g transform="translate(972.287015914917, 548.9571380615234)" class="cluster-label"><foreignObject height="23.98928451538086" width="163.34999084472656"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Batch Processing Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="411.9678535461426" width="547.5883674621582" y="856.9357070922852" x="99.26785659790039" style=""></rect><g transform="translate(310.06204414367676, 856.9357070922852)" class="cluster-label"><foreignObject height="23.98928451538086" width="125.99999237060547"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML Services Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="257.9785690307617" width="623.4294395446777" y="702.9464225769043" x="2399.7418785095215" style=""></rect><g transform="translate(2624.681604385376, 702.9464225769043)" class="cluster-label"><foreignObject height="23.98928451538086" width="173.54998779296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Stream Processing Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="257.9785690307617" width="427.56248474121094" y="394.9678535461426" x="2478.590087890625" style=""></rect><g transform="translate(2615.351692199707, 394.9678535461426)" class="cluster-label"><foreignObject height="23.98928451538086" width="154.03927612304688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Message Broker Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="336.9678535461426" width="501.94106674194336" y="161.98928451538086" x="2926.152572631836" style=""></rect><g transform="translate(3103.821325302124, 161.98928451538086)" class="cluster-label"><foreignObject height="23.98928451538086" width="146.6035614013672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Ingestion Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="103.98928451538086" width="631.4964218139648" y="8" x="2845.609718322754" style=""></rect><g transform="translate(3093.2043647766113, 8)" class="cluster-label"><foreignObject height="23.98928451538086" width="136.30712890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Sources Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_D_0" d="M2966.153,86.989L2966.153,91.156C2966.153,95.323,2966.153,103.656,2966.153,111.989C2966.153,120.323,2966.153,128.656,2966.153,136.989C2966.153,145.323,2966.153,153.656,2999.197,163.822C3032.241,173.989,3098.329,185.989,3131.373,191.988L3164.417,197.988"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D_1" d="M3163.876,86.989L3163.876,91.156C3163.876,95.323,3163.876,103.656,3163.876,111.989C3163.876,120.323,3163.876,128.656,3163.876,136.989C3163.876,145.323,3163.876,153.656,3170.404,161.652C3176.932,169.648,3189.988,177.307,3196.516,181.136L3203.044,184.965"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M3359.081,86.989L3359.081,91.156C3359.081,95.323,3359.081,103.656,3359.081,111.989C3359.081,120.323,3359.081,128.656,3359.081,136.989C3359.081,145.323,3359.081,153.656,3351.14,161.697C3343.2,169.738,3327.318,177.487,3319.377,181.361L3311.436,185.235"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M3252.513,240.979L3252.513,245.145C3252.513,249.312,3252.513,257.645,3252.513,265.312C3252.513,272.979,3252.513,279.979,3252.513,283.479L3252.513,286.979"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M3252.513,344.968L3252.513,349.135C3252.513,353.301,3252.513,361.635,3252.513,369.968C3252.513,378.301,3252.513,386.635,3252.513,394.301C3252.513,401.968,3252.513,408.968,3252.513,412.468L3252.513,415.968"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M3252.513,473.957L3252.513,478.124C3252.513,482.29,3252.513,490.624,3183.663,498.957C3114.813,507.29,2977.112,515.624,2908.262,523.957C2839.412,532.29,2839.412,540.624,2835.435,548.502C2831.458,556.381,2823.505,563.804,2819.528,567.516L2815.551,571.228"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_G_6" d="M2586.67,473.957L2586.67,478.124C2586.67,482.29,2586.67,490.624,2586.67,498.957C2586.67,507.29,2586.67,515.624,2586.67,523.957C2586.67,532.29,2586.67,540.624,2605.784,549.834C2624.898,559.045,2663.127,569.133,2682.241,574.177L2701.356,579.221"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_I_7" d="M2705.223,618.82L2680.241,624.508C2655.259,630.196,2605.295,641.571,2580.313,651.425C2555.331,661.28,2555.331,669.613,2555.331,677.946C2555.331,686.28,2555.331,694.613,2555.331,702.28C2555.331,709.946,2555.331,716.946,2555.331,720.446L2555.331,723.946"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_J_8" d="M2812.627,627.946L2817.091,632.113C2821.555,636.28,2830.483,644.613,2834.947,652.946C2839.412,661.28,2839.412,669.613,2839.412,677.946C2839.412,686.28,2839.412,694.613,2839.412,702.28C2839.412,709.946,2839.412,716.946,2839.412,720.446L2839.412,723.946"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_K_9" d="M2550.139,781.936L2549.338,786.102C2548.537,790.269,2546.934,798.602,2546.133,806.936C2545.331,815.269,2545.331,823.602,2545.331,831.936C2545.331,840.269,2545.331,848.602,2545.331,856.269C2545.331,863.936,2545.331,870.936,2545.331,874.436L2545.331,877.936"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_L_10" d="M2820.253,781.936L2817.296,786.102C2814.339,790.269,2808.425,798.602,2805.468,806.936C2802.511,815.269,2802.511,823.602,2802.511,831.936C2802.511,840.269,2802.511,848.602,2802.511,856.269C2802.511,863.936,2802.511,870.936,2802.511,874.436L2802.511,877.936"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_N_11" d="M2750.287,935.925L2742.226,940.092C2734.165,944.258,2718.044,952.592,2355.801,960.925C1993.558,969.258,1285.194,977.592,931.012,985.925C576.829,994.258,576.829,1002.592,565.988,1010.697C555.147,1018.803,533.465,1026.681,522.624,1030.62L511.783,1034.559"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_12" d="M299.331,935.925L294.917,940.092C290.504,944.258,281.677,952.592,277.263,960.925C272.85,969.258,272.85,977.592,272.85,985.925C272.85,994.258,272.85,1002.592,285.52,1010.853C298.19,1019.115,323.529,1027.304,336.199,1031.399L348.869,1035.494"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_O_13" d="M356.518,935.925L360.931,940.092C365.344,944.258,374.171,952.592,378.585,960.925C382.998,969.258,382.998,977.592,382.998,985.925C382.998,994.258,382.998,1002.592,370.245,1010.88C357.493,1019.168,331.987,1027.411,319.234,1031.533L306.481,1035.655"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_P_14" d="M352.675,1087.582L337.704,1092.138C322.733,1096.693,292.792,1105.804,277.821,1114.526C262.85,1123.248,262.85,1131.581,262.85,1139.914C262.85,1148.248,262.85,1156.581,281.177,1165.101C299.503,1173.62,336.157,1182.326,354.483,1186.679L372.81,1191.032"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_15" d="M1104.714,605.986L943.687,613.812C782.66,621.639,460.605,637.293,299.577,649.286C138.55,661.28,138.55,669.613,138.55,677.946C138.55,686.28,138.55,694.613,138.55,702.28C138.55,709.946,138.55,716.946,138.55,720.446L138.55,723.946"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_S_16" d="M1104.714,610.112L1024.004,617.251C943.294,624.39,781.873,638.668,701.163,649.974C620.453,661.28,620.453,669.613,620.453,677.946C620.453,686.28,620.453,694.613,620.453,702.28C620.453,709.946,620.453,716.946,620.453,720.446L620.453,723.946"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_T_17" d="M1104.714,625.545L1085.483,630.112C1066.251,634.679,1027.787,643.813,1008.556,652.546C989.324,661.28,989.324,669.613,989.324,677.946C989.324,686.28,989.324,694.613,989.324,702.28C989.324,709.946,989.324,716.946,989.324,720.446L989.324,723.946"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_U_18" d="M1311.843,620.04L1341.598,625.525C1371.354,631.009,1430.865,641.978,1460.621,651.629C1490.377,661.28,1490.377,669.613,1490.377,677.946C1490.377,686.28,1490.377,694.613,1490.377,702.28C1490.377,709.946,1490.377,716.946,1490.377,720.446L1490.377,723.946"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_M_19" d="M138.55,781.936L138.55,786.102C138.55,790.269,138.55,798.602,138.55,806.936C138.55,815.269,138.55,823.602,170.112,831.936C201.675,840.269,264.799,848.602,296.362,856.269C327.924,863.936,327.924,870.936,327.924,874.436L327.924,877.936"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_CC_20" d="M528.379,768.347L484.209,774.779C440.038,781.21,351.696,794.073,307.525,804.671C263.354,815.269,263.354,823.602,232.673,831.936C201.992,840.269,140.63,848.602,109.949,861.435C79.268,874.267,79.268,891.599,79.268,908.93C79.268,926.262,79.268,943.593,79.268,956.426C79.268,969.258,79.268,977.592,79.268,985.925C79.268,994.258,79.268,1002.592,79.268,1015.424C79.268,1028.257,79.268,1045.588,79.268,1062.92C79.268,1080.251,79.268,1097.583,79.268,1110.415C79.268,1123.248,79.268,1131.581,283.538,1139.914C487.808,1148.248,896.349,1156.581,1106.767,1164.563C1317.185,1172.545,1329.481,1180.175,1335.629,1183.99L1341.777,1187.805"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_P_21" d="M932.232,781.936L923.419,786.102C914.607,790.269,896.983,798.602,888.17,806.936C879.358,815.269,879.358,823.602,825.603,831.936C771.848,840.269,664.339,848.602,610.584,861.435C556.829,874.267,556.829,891.599,556.829,908.93C556.829,926.262,556.829,943.593,556.829,956.426C556.829,969.258,556.829,977.592,556.829,985.925C556.829,994.258,556.829,1002.592,556.829,1015.424C556.829,1028.257,556.829,1045.588,556.829,1062.92C556.829,1080.251,556.829,1097.583,556.829,1110.415C556.829,1123.248,556.829,1131.581,556.829,1139.914C556.829,1148.248,556.829,1156.581,551.361,1164.535C545.893,1172.488,534.957,1180.063,529.489,1183.85L524.021,1187.637"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_Y_22" d="M1378.482,774.144L1346.636,779.609C1314.79,785.075,1251.099,796.005,1219.253,805.637C1187.407,815.269,1187.407,823.602,1187.407,831.936C1187.407,840.269,1187.407,848.602,1187.407,856.269C1187.407,863.936,1187.407,870.936,1187.407,874.436L1187.407,877.936"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_W_23" d="M2623.884,781.936L2634.465,786.102C2645.046,790.269,2666.209,798.602,2676.79,806.936C2687.371,815.269,2687.371,823.602,2687.371,831.936C2687.371,840.269,2687.371,848.602,2552.174,860.687C2416.977,872.772,2146.582,888.608,2011.385,896.526L1876.188,904.444"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_W_24" d="M2891.077,781.936L2899.052,786.102C2907.027,790.269,2922.976,798.602,2930.951,806.936C2938.926,815.269,2938.926,823.602,2938.926,831.936C2938.926,840.269,2938.926,848.602,2761.803,860.852C2584.681,873.102,2230.436,889.268,2053.313,897.351L1876.19,905.434"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_V_25" d="M1311.843,611.2L1382.156,618.158C1452.47,625.115,1593.097,639.031,1663.411,650.155C1733.724,661.28,1733.724,669.613,1733.724,677.946C1733.724,686.28,1733.724,694.613,1733.724,707.446C1733.724,720.278,1733.724,737.61,1733.724,754.941C1733.724,772.273,1733.724,789.604,1733.724,802.437C1733.724,815.269,1733.724,823.602,1676.997,831.936C1620.269,840.269,1506.814,848.602,1450.086,856.269C1393.359,863.936,1393.359,870.936,1393.359,874.436L1393.359,877.936"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_Z_26" d="M2534.948,935.925L2533.345,940.092C2531.742,944.258,2528.537,952.592,2526.934,960.925C2525.331,969.258,2525.331,977.592,2596.751,985.925C2668.171,994.258,2811.01,1002.592,2882.43,1015.424C2953.85,1028.257,2953.85,1045.588,2953.85,1062.92C2953.85,1080.251,2953.85,1097.583,2953.85,1110.415C2953.85,1123.248,2953.85,1131.581,2953.85,1139.914C2953.85,1148.248,2953.85,1156.581,2969.912,1165.281C2985.973,1173.98,3018.097,1183.046,3034.158,1187.579L3050.22,1192.112"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_Z_27" d="M2796.761,935.925L2795.874,940.092C2794.986,944.258,2793.211,952.592,2792.324,960.925C2791.437,969.258,2791.437,977.592,2842.348,985.925C2893.26,994.258,2995.083,1002.592,3045.995,1015.424C3096.906,1028.257,3096.906,1045.588,3096.906,1062.92C3096.906,1080.251,3096.906,1097.583,3096.906,1110.415C3096.906,1123.248,3096.906,1131.581,3096.906,1139.914C3096.906,1148.248,3096.906,1156.581,3099.792,1164.392C3102.677,1172.202,3108.449,1179.49,3111.335,1183.134L3114.22,1186.778"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_AA_28" d="M433.729,1089.914L433.729,1094.081C433.729,1098.248,433.729,1106.581,433.729,1114.914C433.729,1123.248,433.729,1131.581,433.729,1139.914C433.729,1148.248,433.729,1156.581,544.631,1168.47C655.533,1180.358,877.338,1195.802,988.24,1203.524L1099.143,1211.246"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_BB_29" d="M1311.843,607.58L1429.976,615.141C1548.109,622.702,1784.376,637.824,1902.509,649.552C2020.642,661.28,2020.642,669.613,2020.642,677.946C2020.642,686.28,2020.642,694.613,2020.642,707.446C2020.642,720.278,2020.642,737.61,2020.642,754.941C2020.642,772.273,2020.642,789.604,2020.642,802.437C2020.642,815.269,2020.642,823.602,2020.642,831.936C2020.642,840.269,2020.642,848.602,2020.642,861.435C2020.642,874.267,2020.642,891.599,2020.642,908.93C2020.642,926.262,2020.642,943.593,2020.642,956.426C2020.642,969.258,2020.642,977.592,1921.424,985.925C1822.206,994.258,1623.769,1002.592,1524.551,1015.424C1425.333,1028.257,1425.333,1045.588,1425.333,1062.92C1425.333,1080.251,1425.333,1097.583,1425.333,1110.415C1425.333,1123.248,1425.333,1131.581,1425.333,1139.914C1425.333,1148.248,1425.333,1156.581,1440.392,1164.998C1455.451,1173.414,1485.569,1181.914,1500.628,1186.164L1515.687,1190.415"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_CC_30" d="M2603.5,935.925L2612.479,940.092C2621.457,944.258,2639.414,952.592,2648.393,960.925C2657.371,969.258,2657.371,977.592,2710.118,985.925C2762.864,994.258,2868.357,1002.592,2921.103,1015.424C2973.85,1028.257,2973.85,1045.588,2973.85,1062.92C2973.85,1080.251,2973.85,1097.583,2973.85,1110.415C2973.85,1123.248,2973.85,1131.581,2973.85,1139.914C2973.85,1148.248,2973.85,1156.581,2723.797,1168.949C2473.745,1181.318,1973.64,1197.722,1723.587,1205.924L1473.534,1214.126"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_CC_31" d="M2843.235,935.925L2849.521,940.092C2855.807,944.258,2868.379,952.592,2874.665,960.925C2880.951,969.258,2880.951,977.592,2920.277,985.925C2959.603,994.258,3038.254,1002.592,3077.58,1015.424C3116.906,1028.257,3116.906,1045.588,3116.906,1062.92C3116.906,1080.251,3116.906,1097.583,3116.906,1110.415C3116.906,1123.248,3116.906,1131.581,3116.906,1139.914C3116.906,1148.248,3116.906,1156.581,2843.011,1168.988C2569.116,1181.395,2021.325,1197.875,1747.43,1206.116L1473.535,1214.356"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_CC_32" d="M467.514,1089.914L472.729,1094.081C477.943,1098.248,488.373,1106.581,493.588,1114.914C498.803,1123.248,498.803,1131.581,498.803,1139.914C498.803,1148.248,498.803,1156.581,632.973,1168.587C767.142,1180.593,1035.482,1196.272,1169.652,1204.112L1303.822,1211.951"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_DD_33" d="M1388.676,1243.904L1388.676,1248.07C1388.676,1252.237,1388.676,1260.57,1388.676,1268.904C1388.676,1277.237,1388.676,1285.57,1388.676,1293.237C1388.676,1300.904,1388.676,1307.904,1388.676,1311.404L1388.676,1314.904"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_EE_34" d="M2616.247,935.925L2627.193,940.092C2638.139,944.258,2660.03,952.592,2670.976,960.925C2681.922,969.258,2681.922,977.592,2667.997,985.925C2654.072,994.258,2626.222,1002.592,2612.296,1010.258C2598.371,1017.925,2598.371,1024.925,2598.371,1028.425L2598.371,1031.925"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_GG_35" d="M2854.177,935.925L2862.151,940.092C2870.126,944.258,2886.075,952.592,2894.05,960.925C2902.025,969.258,2902.025,977.592,2887.921,985.925C2873.817,994.258,2845.608,1002.592,2831.504,1010.258C2817.4,1017.925,2817.4,1024.925,2817.4,1028.425L2817.4,1031.925"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_HH_36" d="M712.526,774.98L736.998,780.306C761.47,785.632,810.414,796.284,834.886,805.776C859.358,815.269,859.358,823.602,868.81,831.936C878.261,840.269,897.165,848.602,906.616,861.435C916.068,874.267,916.068,891.599,916.068,908.93C916.068,926.262,916.068,943.593,916.068,956.426C916.068,969.258,916.068,977.592,1024.297,985.925C1132.526,994.258,1348.984,1002.592,1457.213,1010.258C1565.442,1017.925,1565.442,1024.925,1565.442,1028.425L1565.442,1031.925"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_II_37" d="M1018.767,781.936L1023.311,786.102C1027.856,790.269,1036.945,798.602,1041.489,806.936C1046.034,815.269,1046.034,823.602,1046.034,831.936C1046.034,840.269,1046.034,848.602,1046.034,861.435C1046.034,874.267,1046.034,891.599,1046.034,908.93C1046.034,926.262,1046.034,943.593,1046.034,956.426C1046.034,969.258,1046.034,977.592,1172.591,985.925C1299.147,994.258,1552.261,1002.592,1678.817,1010.258C1805.374,1017.925,1805.374,1024.925,1805.374,1028.425L1805.374,1031.925"></path><path marker-end="url(#mermaid-418a1600-fdfe-4912-9696-3bb8ea9af877_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_FF_38" d="M1573.394,781.936L1586.208,786.102C1599.021,790.269,1624.649,798.602,1637.463,806.936C1650.277,815.269,1650.277,823.602,1696.43,831.936C1742.583,840.269,1834.889,848.602,1881.042,861.435C1927.195,874.267,1927.195,891.599,1927.195,908.93C1927.195,926.262,1927.195,943.593,1927.195,956.426C1927.195,969.258,1927.195,977.592,2000.858,985.925C2074.521,994.258,2221.848,1002.592,2295.512,1010.258C2369.175,1017.925,2369.175,1024.925,2369.175,1028.425L2369.175,1031.925"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(2966.152572631836, 59.99464225769043)" id="flowchart-A-313" class="node default"><rect height="53.98928451538086" width="171.08570861816406" y="-26.99464225769043" x="-85.54285430908203" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-55.54285430908203, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="111.08570861816406"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>IoT Sensor Data</p></span></div></foreignObject></g></g><g transform="translate(3163.8757820129395, 59.99464225769043)" id="flowchart-B-314" class="node default"><rect height="53.98928451538086" width="124.36071014404297" y="-26.99464225769043" x="-62.180355072021484" style="" class="basic label-container"></rect><g transform="translate(-32.180355072021484, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="64.36071014404297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>CSV Files</p></span></div></foreignObject></g></g><g transform="translate(3359.08113861084, 59.99464225769043)" id="flowchart-C-315" class="node default"><rect height="53.98928451538086" width="166.04999542236328" y="-26.99464225769043" x="-83.02499771118164" style="" class="basic label-container"></rect><g transform="translate(-53.02499771118164, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="106.04999542236328"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Historical Data</p></span></div></foreignObject></g></g><g transform="translate(3252.5132846832275, 213.9839267730713)" id="flowchart-D-316" class="node default"><rect height="53.98928451538086" width="168.32141876220703" y="-26.99464225769043" x="-84.16070938110352" style="" class="basic label-container"></rect><g transform="translate(-54.160709381103516, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="108.32141876220703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Kafka Producer</p></span></div></foreignObject></g></g><g transform="translate(3252.5132846832275, 317.97321128845215)" id="flowchart-E-317" class="node default"><rect height="53.98928451538086" width="169.15713500976562" y="-26.99464225769043" x="-84.57856750488281" style="" class="basic label-container"></rect><g transform="translate(-54.57856750488281, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="109.15713500976562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Validation</p></span></div></foreignObject></g></g><g transform="translate(3252.5132846832275, 446.962495803833)" id="flowchart-F-318" class="node default"><rect height="53.98928451538086" width="201.91070556640625" y="-26.99464225769043" x="-100.95535278320312" style="" class="basic label-container"></rect><g transform="translate(-70.95535278320312, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="141.91070556640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Message Formatting</p></span></div></foreignObject></g></g><g transform="translate(2783.7052574157715, 600.9517803192139)" id="flowchart-G-319" class="node default"><rect height="53.98928451538086" width="156.9642791748047" y="-26.99464225769043" x="-78.48213958740234" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-48.482139587402344, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="96.96427917480469"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Apache Kafka</p></span></div></foreignObject></g></g><g transform="translate(2586.669548034668, 446.962495803833)" id="flowchart-H-320" class="node default"><rect height="53.98928451538086" width="135.35356903076172" y="-26.99464225769043" x="-67.67678451538086" style="" class="basic label-container"></rect><g transform="translate(-37.67678451538086, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="75.35356903076172"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Zookeeper</p></span></div></foreignObject></g></g><g transform="translate(2555.331157684326, 754.9410648345947)" id="flowchart-I-321" class="node default"><rect height="53.98928451538086" width="241.17855834960938" y="-26.99464225769043" x="-120.58927917480469" style="" class="basic label-container"></rect><g transform="translate(-90.58927917480469, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="181.17855834960938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Regular Stream Processor</p></span></div></foreignObject></g></g><g transform="translate(2839.4115028381348, 754.9410648345947)" id="flowchart-J-322" class="node default"><rect height="53.98928451538086" width="226.9821319580078" y="-26.99464225769043" x="-113.4910659790039" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-83.4910659790039, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="166.9821319580078"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Spark Stream Processor</p></span></div></foreignObject></g></g><g transform="translate(2545.331157684326, 908.9303493499756)" id="flowchart-K-323" class="node default"><rect height="53.98928451538086" width="197.7321319580078" y="-26.99464225769043" x="-98.8660659790039" style="" class="basic label-container"></rect><g transform="translate(-68.8660659790039, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="137.7321319580078"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Real-time Analytics</p></span></div></foreignObject></g></g><g transform="translate(2802.510612487793, 908.9303493499756)" id="flowchart-L-324" class="node default"><rect height="53.98928451538086" width="162.8249969482422" y="-26.99464225769043" x="-81.4124984741211" style="" class="basic label-container"></rect><g transform="translate(-51.412498474121094, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="102.82499694824219"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML Integration</p></span></div></foreignObject></g></g><g transform="translate(327.9240951538086, 908.9303493499756)" id="flowchart-M-325" class="node default"><rect height="53.98928451538086" width="190.29641723632812" y="-26.99464225769043" x="-95.14820861816406" style="" class="basic label-container"></rect><g transform="translate(-65.14820861816406, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="130.29641723632812"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML Trainer Service</p></span></div></foreignObject></g></g><g transform="translate(433.72855377197266, 1062.9196338653564)" id="flowchart-N-326" class="node default"><rect height="53.98928451538086" width="162.10713958740234" y="-26.99464225769043" x="-81.05356979370117" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-51.05356979370117, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="102.10713958740234"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML API Service</p></span></div></foreignObject></g></g><g transform="translate(222.11963653564453, 1062.9196338653564)" id="flowchart-O-327" class="node default"><rect height="53.98928451538086" width="161.11071014404297" y="-26.99464225769043" x="-80.55535507202148" style="" class="basic label-container"></rect><g transform="translate(-50.555355072021484, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="101.11071014404297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Model Storage</p></span></div></foreignObject></g></g><g transform="translate(481.7553367614746, 1216.9089183807373)" id="flowchart-P-328" class="node default"><rect height="53.98928451538086" width="210.1071319580078" y="-26.99464225769043" x="-105.0535659790039" style="" class="basic label-container"></rect><g transform="translate(-75.0535659790039, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="150.1071319580078"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Performance Monitor</p></span></div></foreignObject></g></g><g transform="translate(1208.2785377502441, 600.9517803192139)" id="flowchart-Q-329" class="node default"><rect height="53.98928451538086" width="207.12855529785156" y="-26.99464225769043" x="-103.56427764892578" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-73.56427764892578, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="147.12855529785156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Airflow Orchestrator</p></span></div></foreignObject></g></g><g transform="translate(138.54999160766602, 754.9410648345947)" id="flowchart-R-330" class="node default"><rect height="53.98928451538086" width="175.07141876220703" y="-26.99464225769043" x="-87.53570938110352" style="" class="basic label-container"></rect><g transform="translate(-57.535709381103516, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="115.07141876220703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML Training DAG</p></span></div></foreignObject></g></g><g transform="translate(620.4526481628418, 754.9410648345947)" id="flowchart-S-331" class="node default"><rect height="53.98928451538086" width="184.14642333984375" y="-26.99464225769043" x="-92.07321166992188" style="" class="basic label-container"></rect><g transform="translate(-62.073211669921875, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="124.14642333984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Quality DAG</p></span></div></foreignObject></g></g><g transform="translate(989.3240623474121, 754.9410648345947)" id="flowchart-T-332" class="node default"><rect height="53.98928451538086" width="185.79642486572266" y="-26.99464225769043" x="-92.89821243286133" style="" class="basic label-container"></rect><g transform="translate(-62.89821243286133, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="125.79642486572266"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Performance DAG</p></span></div></foreignObject></g></g><g transform="translate(1490.3767395019531, 754.9410648345947)" id="flowchart-U-333" class="node default"><rect height="53.98928451538086" width="223.78927612304688" y="-26.99464225769043" x="-111.89463806152344" style="" class="basic label-container"></rect><g transform="translate(-81.89463806152344, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="163.78927612304688"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Historical Analysis DAG</p></span></div></foreignObject></g></g><g transform="translate(1393.3588752746582, 908.9303493499756)" id="flowchart-V-334" class="node default"><rect height="53.98928451538086" width="139.15713500976562" y="-26.99464225769043" x="-69.57856750488281" style="" class="basic label-container"></rect><g transform="translate(-39.57856750488281, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="79.15713500976562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(1799.5785026550293, 908.9303493499756)" id="flowchart-W-335" class="node default"><rect height="53.98928451538086" width="145.23213958740234" y="-26.99464225769043" x="-72.61606979370117" style="" class="basic label-container"></rect><g transform="translate(-42.61606979370117, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="85.23213958740234"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>File Storage</p></span></div></foreignObject></g></g><g transform="translate(1594.9499397277832, 908.9303493499756)" id="flowchart-X-336" class="node default"><rect height="53.98928451538086" width="164.02499389648438" y="-26.99464225769043" x="-82.01249694824219" style="" class="basic label-container"></rect><g transform="translate(-52.01249694824219, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="104.02499389648438"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Model Registry</p></span></div></foreignObject></g></g><g transform="translate(1187.4070930480957, 908.9303493499756)" id="flowchart-Y-337" class="node default"><rect height="53.98928451538086" width="172.74642181396484" y="-26.99464225769043" x="-86.37321090698242" style="" class="basic label-container"></rect><g transform="translate(-56.37321090698242, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="112.74642181396484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reports Storage</p></span></div></foreignObject></g></g><g transform="translate(3138.0802574157715, 1216.9089183807373)" id="flowchart-Z-338" class="node default"><rect height="53.98928451538086" width="168.02142333984375" y="-26.99464225769043" x="-84.01071166992188" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-54.010711669921875, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="108.02142333984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Spark UI - 4040</p></span></div></foreignObject></g></g><g transform="translate(1180.4740600585938, 1216.9089183807373)" id="flowchart-AA-339" class="node default"><rect height="53.98928451538086" width="154.68213653564453" y="-26.99464225769043" x="-77.34106826782227" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-47.341068267822266, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="94.68213653564453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML API - 5000</p></span></div></foreignObject></g></g><g transform="translate(1609.5633392333984, 1216.9089183807373)" id="flowchart-BB-340" class="node default"><rect height="53.98928451538086" width="180.0535659790039" y="-26.99464225769043" x="-90.02678298950195" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-60.02678298950195, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="120.0535659790039"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Airflow UI - 8080</p></span></div></foreignObject></g></g><g transform="translate(1388.6758422851562, 1216.9089183807373)" id="flowchart-CC-341" class="node default"><rect height="53.98928451538086" width="161.72142028808594" y="-26.99464225769043" x="-80.86071014404297" style="" class="basic label-container"></rect><g transform="translate(-50.86071014404297, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="101.72142028808594"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Health Checks</p></span></div></foreignObject></g></g><g transform="translate(1388.6758422851562, 1345.8982028961182)" id="flowchart-DD-342" class="node default"><rect height="53.98928451538086" width="180.1285629272461" y="-26.99464225769043" x="-90.06428146362305" style="" class="basic label-container"></rect><g transform="translate(-60.06428146362305, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="120.1285629272461"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert Generation</p></span></div></foreignObject></g></g><g transform="translate(2598.3713455200195, 1062.9196338653564)" id="flowchart-EE-343" class="node default"><rect height="53.98928451538086" width="175.15713500976562" y="-26.99464225769043" x="-87.57856750488281" style="" class="basic label-container"></rect><g transform="translate(-57.57856750488281, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="115.15713500976562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Real-time Alerts</p></span></div></foreignObject></g></g><g transform="translate(2369.1749267578125, 1062.9196338653564)" id="flowchart-FF-344" class="node default"><rect height="53.98928451538086" width="183.23571014404297" y="-26.99464225769043" x="-91.61785507202148" style="" class="basic label-container"></rect><g transform="translate(-61.617855072021484, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="123.23571014404297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Analytics Reports</p></span></div></foreignObject></g></g><g transform="translate(2817.3999099731445, 1062.9196338653564)" id="flowchart-GG-345" class="node default"><rect height="53.98928451538086" width="162.89999389648438" y="-26.99464225769043" x="-81.44999694824219" style="" class="basic label-container"></rect><g transform="translate(-51.44999694824219, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="102.89999389648438"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML Predictions</p></span></div></foreignObject></g></g><g transform="translate(1565.4419136047363, 1062.9196338653564)" id="flowchart-HH-346" class="node default"><rect height="53.98928451538086" width="170.2178497314453" y="-26.99464225769043" x="-85.10892486572266" style="" class="basic label-container"></rect><g transform="translate(-55.108924865722656, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="110.21784973144531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Quality Reports</p></span></div></foreignObject></g></g><g transform="translate(1805.3740501403809, 1062.9196338653564)" id="flowchart-II-347" class="node default"><rect height="53.98928451538086" width="209.64642333984375" y="-26.99464225769043" x="-104.82321166992188" style="" class="basic label-container"></rect><g transform="translate(-74.82321166992188, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="149.64642333984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Performance Reports</p></span></div></foreignObject></g></g></g></g></g></svg>