# IoT Smoke Detection Pipeline - Complete Requirements
# Python version 3.10.xx

# Core data processing
numpy==1.24.3
pandas==2.0.3
pyarrow==12.0.1
scipy==1.10.1

# Machine Learning
scikit-learn==1.3.2
joblib==1.3.2

# Configuration and utilities
python-dotenv==1.0.0
pyyaml==6.0.1
python-dateutil==2.9.0.post0
watchdog==4.0.0

# Streaming and messaging
kafka-python==2.0.2
pyspark==3.4.1
py4j==********

# Visualization
matplotlib==3.6.3
seaborn==0.12.2

# Web framework (Flask API)
werkzeug==2.2.3
flask==2.2.5
flask-cors==3.0.10
gunicorn==20.1.0
requests==2.31.0
schedule==1.2.0

# Monitoring
prometheus-client==0.17.1

# Database
psycopg2-binary==2.9.7
sqlalchemy==1.4.48

# Airflow workflow management
packaging==24.1
rich==13.7.1
apache-airflow==2.8.1
apache-airflow-providers-postgres==5.8.0

# Optional dependencies (commented out to avoid conflicts)
# great_expectations==0.18.11  # Data validation - removed due to dependency conflicts
# jupyter==1.0.0  # Notebook support - simplified to avoid dependency conflicts
