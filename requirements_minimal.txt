# Minimal requirements for IoT Smoke Detection Pipeline
# Core data processing
numpy==1.24.3
pandas==2.0.3
pyarrow==12.0.1
scipy==1.10.1

# Machine Learning
scikit-learn==1.3.2
joblib==1.3.2

# Configuration
python-dotenv==1.0.0

# Streaming and messaging
kafka-python==2.0.2
pyspark==3.4.1
py4j==********

# Visualization (minimal)
matplotlib==3.6.3
seaborn==0.12.2

# Web framework (compatible versions)
werkzeug==2.2.3
flask==2.2.5
flask-cors==3.0.10
gunicorn==20.1.0
requests==2.31.0
schedule==1.2.0

# Monitoring
prometheus-client==0.17.1

# Database
psycopg2-binary==2.9.7
sqlalchemy==1.4.48

# YAML support
pyyaml==6.0
