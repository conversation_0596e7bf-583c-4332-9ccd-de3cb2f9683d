"""
Error handling utilities for stream processing components.
Provides retry mechanisms, validation, and logging for robust stream processing.
"""

import logging
import time
from functools import wraps
from kafka.errors import KafkaError, NoBrokersAvailable
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("stream_processing.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("error_handler")


def retry_on_kafka_error(max_retries=5, delay=5):
    """Decorator to retry operations on Kafka errors"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except NoBrokersAvailable as e:
                    retries += 1
                    logger.warning(f"Kafka brokers not available. Retry {retries}/{max_retries} in {delay}s")
                    if retries == max_retries:
                        logger.error(f"Failed to connect to Kafka after {max_retries} attempts")
                        raise
                    time.sleep(delay)
                except KafkaError as e:
                    logger.error(f"Kafka error: {str(e)}")
                    raise
                except Exception as e:
                    logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
                    raise
            return wrapper
        return decorator
    return decorator


def validate_sensor_data(data: Dict[str, Any]) -> tuple[bool, List[str]]:
    """
    Validate sensor data structure and values
    
    Args:
        data: Dictionary containing sensor readings
        
    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    
    # Required fields
    required_fields = [
        "Temperature[C]", "Humidity[%]", "TVOC[ppb]", "eCO2[ppm]",
        "Raw H2", "Raw Ethanol", "Pressure[hPa]", "PM1.0", "PM2.5",
        "NC0.5", "NC1.0", "NC2.5", "CNT", "Fire Alarm"
    ]
    
    # Check for missing fields
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        errors.append(f"Missing required fields: {missing_fields}")
    
    # Validate numeric fields
    numeric_fields = [
        "Temperature[C]", "Humidity[%]", "TVOC[ppb]", "eCO2[ppm]",
        "Raw H2", "Raw Ethanol", "Pressure[hPa]", "PM1.0", "PM2.5",
        "NC0.5", "NC1.0", "NC2.5", "CNT"
    ]
    
    for field in numeric_fields:
        if field in data and data[field] is not None:
            try:
                value = float(data[field])
                
                # Range validation
                if field == "Temperature[C]" and not (-50 <= value <= 100):
                    errors.append(f"Temperature out of range: {value}°C")
                elif field == "Humidity[%]" and not (0 <= value <= 100):
                    errors.append(f"Humidity out of range: {value}%")
                elif field == "Pressure[hPa]" and not (800 <= value <= 1200):
                    errors.append(f"Pressure out of range: {value} hPa")
                elif field in ["TVOC[ppb]", "eCO2[ppm]"] and value < 0:
                    errors.append(f"Negative value for {field}: {value}")
                elif field.startswith("PM") and value < 0:
                    errors.append(f"Negative particulate matter value for {field}: {value}")
                elif field.startswith("NC") and value < 0:
                    errors.append(f"Negative particle count for {field}: {value}")
                    
            except (ValueError, TypeError):
                errors.append(f"Invalid numeric value for {field}: {data[field]}")
    
    # Validate Fire Alarm field
    if "Fire Alarm" in data:
        if data["Fire Alarm"] not in [0, 1, "0", "1"]:
            errors.append(f"Invalid Fire Alarm value: {data['Fire Alarm']} (must be 0 or 1)")
    
    return len(errors) == 0, errors


def sanitize_sensor_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sanitize sensor data by handling missing values and outliers
    
    Args:
        data: Raw sensor data dictionary
        
    Returns:
        Sanitized data dictionary
    """
    sanitized = data.copy()
    
    # Default values for missing numeric fields
    defaults = {
        "Temperature[C]": 20.0,
        "Humidity[%]": 50.0,
        "TVOC[ppb]": 0.0,
        "eCO2[ppm]": 400.0,
        "Raw H2": 0.0,
        "Raw Ethanol": 0.0,
        "Pressure[hPa]": 1013.25,
        "PM1.0": 0.0,
        "PM2.5": 0.0,
        "NC0.5": 0.0,
        "NC1.0": 0.0,
        "NC2.5": 0.0,
        "CNT": 0,
        "Fire Alarm": 0
    }
    
    for field, default_value in defaults.items():
        if field not in sanitized or sanitized[field] is None:
            sanitized[field] = default_value
            logger.debug(f"Set default value for {field}: {default_value}")
        else:
            try:
                # Convert to appropriate type
                if field == "Fire Alarm" or field == "CNT":
                    sanitized[field] = int(float(sanitized[field]))
                else:
                    sanitized[field] = float(sanitized[field])
                    
                # Handle extreme outliers
                if field == "Temperature[C]":
                    if sanitized[field] < -50 or sanitized[field] > 100:
                        logger.warning(f"Temperature outlier detected: {sanitized[field]}°C, using default")
                        sanitized[field] = defaults[field]
                elif field == "Humidity[%]":
                    if sanitized[field] < 0 or sanitized[field] > 100:
                        logger.warning(f"Humidity outlier detected: {sanitized[field]}%, using default")
                        sanitized[field] = defaults[field]
                        
            except (ValueError, TypeError):
                logger.warning(f"Invalid value for {field}: {sanitized[field]}, using default")
                sanitized[field] = default_value
    
    return sanitized


class StreamProcessingError(Exception):
    """Custom exception for stream processing errors"""
    pass


class DataValidationError(StreamProcessingError):
    """Exception raised for data validation errors"""
    pass


class KafkaConnectionError(StreamProcessingError):
    """Exception raised for Kafka connection issues"""
    pass


def handle_processing_error(error: Exception, context: str = ""):
    """
    Central error handler for stream processing
    
    Args:
        error: The exception that occurred
        context: Additional context about where the error occurred
    """
    error_msg = f"Error in {context}: {str(error)}" if context else f"Error: {str(error)}"
    
    if isinstance(error, NoBrokersAvailable):
        logger.error(f"Kafka connection failed: {error_msg}")
        raise KafkaConnectionError(error_msg) from error
    elif isinstance(error, KafkaError):
        logger.error(f"Kafka error: {error_msg}")
        raise StreamProcessingError(error_msg) from error
    elif isinstance(error, (ValueError, TypeError)):
        logger.error(f"Data validation error: {error_msg}")
        raise DataValidationError(error_msg) from error
    else:
        logger.error(f"Unexpected error: {error_msg}")
        raise StreamProcessingError(error_msg) from error


def log_processing_metrics(processed_count: int, error_count: int, start_time: float):
    """
    Log processing metrics for monitoring
    
    Args:
        processed_count: Number of successfully processed messages
        error_count: Number of messages that failed processing
        start_time: Processing start time (from time.time())
    """
    duration = time.time() - start_time
    total_messages = processed_count + error_count
    
    if total_messages > 0:
        success_rate = (processed_count / total_messages) * 100
        throughput = total_messages / duration if duration > 0 else 0
        
        logger.info(f"Processing Metrics - "
                   f"Total: {total_messages}, "
                   f"Success: {processed_count}, "
                   f"Errors: {error_count}, "
                   f"Success Rate: {success_rate:.2f}%, "
                   f"Throughput: {throughput:.2f} msg/sec, "
                   f"Duration: {duration:.2f}s")
    else:
        logger.info("No messages processed in this interval")
