# Import required libraries
import sys
from pathlib import Path

# Add project root to path
project_root = Path().resolve().parents[1]
sys.path.append(str(project_root))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
from datetime import datetime

# ML libraries
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression

# Project imports
from ml.training.train_model import SmokeDetectionTrainer
from ml.inference.model_loader import get_model_loader
from ml.inference.predict import SmokeDetectionPredictor, create_sample_data, create_fire_scenario_data
from config.constants import CLEANED_DATA_FILE, TARGET_COLUMN
from app.utils.path_utils import DATA_DIR, build_relative_path

# Configure plotting
plt.style.use('default')
sns.set_palette("husl")
%matplotlib inline

# Load the dataset
data_path = build_relative_path(DATA_DIR, CLEANED_DATA_FILE)
df = pd.read_csv(data_path)

print(f"Dataset shape: {df.shape}")
print(f"Target column: {TARGET_COLUMN}")
print("\nFirst few rows:")
df.head()

# Basic statistics
print("Dataset Info:")
print(df.info())
print("\nTarget distribution:")
print(df[TARGET_COLUMN].value_counts())
print("\nMissing values:")
print(df.isnull().sum())

# Visualize target distribution
plt.figure(figsize=(10, 6))

plt.subplot(1, 2, 1)
df[TARGET_COLUMN].value_counts().plot(kind='bar')
plt.title('Target Distribution')
plt.xlabel('Fire Alarm')
plt.ylabel('Count')

plt.subplot(1, 2, 2)
df[TARGET_COLUMN].value_counts().plot(kind='pie', autopct='%1.1f%%')
plt.title('Target Distribution (%)')

plt.tight_layout()
plt.show()

# Correlation analysis
plt.figure(figsize=(12, 10))
correlation_matrix = df.corr()
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, fmt='.2f')
plt.title('Feature Correlation Matrix')
plt.tight_layout()
plt.show()

# Initialize trainer for feature engineering
trainer = SmokeDetectionTrainer()

# Prepare features and target
feature_columns = [col for col in df.columns if col != TARGET_COLUMN]
X = df[feature_columns]
y = df[TARGET_COLUMN]

print(f"Original features: {len(feature_columns)}")
print("Feature columns:", feature_columns)

# Create advanced features
X_enhanced = trainer.create_advanced_features(X)
print(f"\nEnhanced features: {len(X_enhanced.columns)}")
print(f"Added {len(X_enhanced.columns) - len(feature_columns)} new features")

# Visualize some key features
fig, axes = plt.subplots(2, 3, figsize=(15, 10))
axes = axes.ravel()

key_features = ['Temperature[C]', 'Humidity[%]', 'TVOC[ppb]', 'eCO2[ppm]', 'PM2.5', 'Pressure[hPa]']

for i, feature in enumerate(key_features):
    if feature in df.columns:
        for target_val in df[TARGET_COLUMN].unique():
            data = df[df[TARGET_COLUMN] == target_val][feature]
            axes[i].hist(data, alpha=0.7, label=f'Fire Alarm = {target_val}', bins=30)
        
        axes[i].set_title(f'{feature} Distribution')
        axes[i].set_xlabel(feature)
        axes[i].set_ylabel('Frequency')
        axes[i].legend()

plt.tight_layout()
plt.show()

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    X_enhanced, y, test_size=0.2, random_state=42, stratify=y
)

print(f"Training set: {X_train.shape}")
print(f"Test set: {X_test.shape}")
print(f"Training target distribution: {y_train.value_counts().to_dict()}")
print(f"Test target distribution: {y_test.value_counts().to_dict()}")

# Train models
print("Training models...")
results = trainer.train_models(X_train, X_test, y_train, y_test)

# Display results
print("\nTraining Results:")
for model_name, result in results.items():
    print(f"\n{model_name.upper()}:")
    print(f"  Best Parameters: {result['best_params']}")
    print(f"  CV Score: {result['best_score']:.4f}")
    for metric, value in result['metrics'].items():
        if value is not None:
            print(f"  {metric.capitalize()}: {value:.4f}")

# Create visualizations
trainer.create_visualizations(results, y_test, save_plots=False)

# Get best model
best_model_name = trainer.best_model_name
best_model = trainer.best_model

print(f"Best Model: {best_model_name}")
print(f"Model Type: {type(best_model).__name__}")

# Make predictions
if trainer.scalers.get('standard'):
    X_test_scaled = trainer.scalers['standard'].transform(X_test)
else:
    X_test_scaled = X_test

y_pred = best_model.predict(X_test_scaled)
y_pred_proba = best_model.predict_proba(X_test_scaled)[:, 1] if hasattr(best_model, 'predict_proba') else None

# Classification report
print("\nClassification Report:")
print(classification_report(y_test, y_pred))

# Confusion matrix
cm = confusion_matrix(y_test, y_pred)
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
plt.title(f'Confusion Matrix - {best_model_name}')
plt.xlabel('Predicted')
plt.ylabel('Actual')
plt.show()

# Save the trained model
model_path = trainer.save_model()
print(f"Model saved to: {model_path}")

# Load the saved model
predictor = SmokeDetectionPredictor(model_path)
print("Model loaded successfully")
print("Model info:", predictor.get_model_info())

# Example 1: Normal conditions
normal_data = create_sample_data()
print("Normal sensor data:")
print(json.dumps(normal_data, indent=2))

prediction = predictor.predict_single(normal_data)
print("\nPrediction:")
print(json.dumps(prediction, indent=2))

# Example 2: Fire scenario
fire_data = create_fire_scenario_data()
print("Fire scenario sensor data:")
print(json.dumps(fire_data, indent=2))

fire_prediction = predictor.predict_single(fire_data)
print("\nPrediction:")
print(json.dumps(fire_prediction, indent=2))

# Batch prediction example
batch_data = [normal_data, fire_data]
batch_predictions = predictor.predict_batch(batch_data)

print("Batch predictions:")
for i, pred in enumerate(batch_predictions):
    print(f"\nSample {i+1}: {pred['prediction_label']} (Confidence: {pred.get('confidence', 'N/A')})")

# API usage examples (requires running Flask server)
import requests

# Note: These examples assume the Flask API is running on localhost:5000
API_BASE_URL = "http://localhost:5000"

def test_api_endpoint(endpoint, method='GET', data=None):
    """Test API endpoint (will fail if server not running)"""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        if method == 'GET':
            response = requests.get(url, timeout=5)
        else:
            response = requests.post(url, json=data, timeout=5)
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API call failed: {e}")
        print("Make sure the Flask API server is running: python ml/inference/predict_wrapper.py")
        return None

# Test health endpoint
print("Testing health endpoint:")
test_api_endpoint("/health")

# Test prediction endpoint
print("Testing prediction endpoint with normal data:")
test_api_endpoint("/predict", method='POST', data=normal_data)

print("\nTesting prediction endpoint with fire scenario:")
test_api_endpoint("/predict", method='POST', data=fire_data)

# Test sample prediction endpoints
print("Testing sample prediction endpoint:")
test_api_endpoint("/predict/sample")

print("\nTesting fire scenario endpoint:")
test_api_endpoint("/predict/fire-scenario")

# Stream integration example
print("Stream ML Integration Example:")
print("\nTo run real-time ML predictions on streaming data:")
print("1. Start Kafka and the data producer:")
print("   docker-compose up -d kafka smoke_kafka_producer")
print("\n2. Run the stream ML processor:")
print("   python ml/inference/stream_ml_integration.py --model path/to/model.pkl")
print("\n3. Monitor predictions:")
print("   python ml/inference/stream_ml_integration.py --monitor")

# Simulate stream processing
print("\nSimulating stream processing with sample data:")
stream_samples = [normal_data, fire_data, normal_data]

for i, sample in enumerate(stream_samples):
    prediction = predictor.predict_single(sample)
    print(f"\nStream Sample {i+1}:")
    print(f"  Prediction: {prediction['prediction_label']}")
    print(f"  Confidence: {prediction.get('confidence', 'N/A')}")
    if prediction.get('prediction') == 1:
        print(f"  🚨 FIRE ALERT! 🚨")