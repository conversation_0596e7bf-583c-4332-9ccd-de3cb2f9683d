{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IoT Smoke Detection ML Workflow\n", "\n", "This notebook demonstrates the complete machine learning workflow for IoT smoke detection, including:\n", "- Data exploration and preprocessing\n", "- Feature engineering\n", "- Model training and evaluation\n", "- Prediction and API usage\n", "\n", "## Table of Contents\n", "1. [Data Loading and Exploration](#data-loading)\n", "2. [Feature Engineering](#feature-engineering)\n", "3. [Model Training](#model-training)\n", "4. [Model Evaluation](#model-evaluation)\n", "5. [Prediction Examples](#prediction-examples)\n", "6. [API Usage](#api-usage)\n", "7. [Stream Integration](#stream-integration)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add project root to path\n", "project_root = Path().resolve().parents[1]\n", "sys.path.append(str(project_root))\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import json\n", "from datetime import datetime\n", "\n", "# ML libraries\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "\n", "# Project imports\n", "from ml.training.train_model import SmokeDetectionTrainer\n", "from ml.inference.model_loader import get_model_loader\n", "from ml.inference.predict import SmokeDetectionPredictor, create_sample_data, create_fire_scenario_data\n", "from config.constants import CLEANED_DATA_FILE, TARGET_COLUMN\n", "from app.utils.path_utils import DATA_DIR, build_relative_path\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Exploration {#data-loading}\n", "\n", "Let's start by loading and exploring the smoke detection dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "data_path = build_relative_path(DATA_DIR, CLEANED_DATA_FILE)\n", "df = pd.read_csv(data_path)\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Target column: {TARGET_COLUMN}\")\n", "print(\"\\nFirst few rows:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic statistics\n", "print(\"Dataset Info:\")\n", "print(df.info())\n", "print(\"\\nTarget distribution:\")\n", "print(df[TARGET_COLUMN].value_counts())\n", "print(\"\\nMissing values:\")\n", "print(df.isnull().sum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize target distribution\n", "plt.figure(figsize=(10, 6))\n", "\n", "plt.subplot(1, 2, 1)\n", "df[TARGET_COLUMN].value_counts().plot(kind='bar')\n", "plt.title('Target Distribution')\n", "plt.xlabel('Fire Alarm')\n", "plt.ylabel('Count')\n", "\n", "plt.subplot(1, 2, 2)\n", "df[TARGET_COLUMN].value_counts().plot(kind='pie', autopct='%1.1f%%')\n", "plt.title('Target Distribution (%)')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Correlation analysis\n", "plt.figure(figsize=(12, 10))\n", "correlation_matrix = df.corr()\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, fmt='.2f')\n", "plt.title('Feature Correlation Matrix')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Feature Engineering {#feature-engineering}\n", "\n", "We'll create advanced features to improve model performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize trainer for feature engineering\n", "trainer = SmokeDetectionTrainer()\n", "\n", "# Prepare features and target\n", "feature_columns = [col for col in df.columns if col != TARGET_COLUMN]\n", "X = df[feature_columns]\n", "y = df[TARGET_COLUMN]\n", "\n", "print(f\"Original features: {len(feature_columns)}\")\n", "print(\"Feature columns:\", feature_columns)\n", "\n", "# Create advanced features\n", "X_enhanced = trainer.create_advanced_features(X)\n", "print(f\"\\nEnhanced features: {len(X_enhanced.columns)}\")\n", "print(f\"Added {len(X_enhanced.columns) - len(feature_columns)} new features\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize some key features\n", "fig, axes = plt.subplots(2, 3, figsize=(15, 10))\n", "axes = axes.ravel()\n", "\n", "key_features = ['Temperature[C]', 'Humidity[%]', 'TVOC[ppb]', 'eCO2[ppm]', 'PM2.5', 'Pressure[hPa]']\n", "\n", "for i, feature in enumerate(key_features):\n", "    if feature in df.columns:\n", "        for target_val in df[TARGET_COLUMN].unique():\n", "            data = df[df[TARGET_COLUMN] == target_val][feature]\n", "            axes[i].hist(data, alpha=0.7, label=f'Fire Alarm = {target_val}', bins=30)\n", "        \n", "        axes[i].set_title(f'{feature} Distribution')\n", "        axes[i].set_xlabel(feature)\n", "        axes[i].set_ylabel('Frequency')\n", "        axes[i].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Model Training {#model-training}\n", "\n", "Train multiple models and compare their performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_enhanced, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "print(f\"Training set: {X_train.shape}\")\n", "print(f\"Test set: {X_test.shape}\")\n", "print(f\"Training target distribution: {y_train.value_counts().to_dict()}\")\n", "print(f\"Test target distribution: {y_test.value_counts().to_dict()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train models\n", "print(\"Training models...\")\n", "results = trainer.train_models(X_train, X_test, y_train, y_test)\n", "\n", "# Display results\n", "print(\"\\nTraining Results:\")\n", "for model_name, result in results.items():\n", "    print(f\"\\n{model_name.upper()}:\")\n", "    print(f\"  Best Parameters: {result['best_params']}\")\n", "    print(f\"  CV Score: {result['best_score']:.4f}\")\n", "    for metric, value in result['metrics'].items():\n", "        if value is not None:\n", "            print(f\"  {metric.capitalize()}: {value:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create visualizations\n", "trainer.create_visualizations(results, y_test, save_plots=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Model Evaluation {#model-evaluation}\n", "\n", "Detailed evaluation of the best performing model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get best model\n", "best_model_name = trainer.best_model_name\n", "best_model = trainer.best_model\n", "\n", "print(f\"Best Model: {best_model_name}\")\n", "print(f\"Model Type: {type(best_model).__name__}\")\n", "\n", "# Make predictions\n", "if trainer.scalers.get('standard'):\n", "    X_test_scaled = trainer.scalers['standard'].transform(X_test)\n", "else:\n", "    X_test_scaled = X_test\n", "\n", "y_pred = best_model.predict(X_test_scaled)\n", "y_pred_proba = best_model.predict_proba(X_test_scaled)[:, 1] if hasattr(best_model, 'predict_proba') else None\n", "\n", "# Classification report\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, y_pred))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(y_test, y_pred)\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')\n", "plt.title(f'Confusion Matrix - {best_model_name}')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the trained model\n", "model_path = trainer.save_model()\n", "print(f\"Model saved to: {model_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Prediction Examples {#prediction-examples}\n", "\n", "Demonstrate how to use the trained model for predictions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the saved model\n", "predictor = SmokeDetectionPredictor(model_path)\n", "print(\"Model loaded successfully\")\n", "print(\"Model info:\", predictor.get_model_info())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 1: Normal conditions\n", "normal_data = create_sample_data()\n", "print(\"Normal sensor data:\")\n", "print(json.dumps(normal_data, indent=2))\n", "\n", "prediction = predictor.predict_single(normal_data)\n", "print(\"\\nPrediction:\")\n", "print(json.dumps(prediction, indent=2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 2: Fire scenario\n", "fire_data = create_fire_scenario_data()\n", "print(\"Fire scenario sensor data:\")\n", "print(json.dumps(fire_data, indent=2))\n", "\n", "fire_prediction = predictor.predict_single(fire_data)\n", "print(\"\\nPrediction:\")\n", "print(json.dumps(fire_prediction, indent=2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Batch prediction example\n", "batch_data = [normal_data, fire_data]\n", "batch_predictions = predictor.predict_batch(batch_data)\n", "\n", "print(\"Batch predictions:\")\n", "for i, pred in enumerate(batch_predictions):\n", "    print(f\"\\nSample {i+1}: {pred['prediction_label']} (Confidence: {pred.get('confidence', 'N/A')})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. API Usage {#api-usage}\n", "\n", "Examples of how to use the Flask API for predictions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# API usage examples (requires running Flask server)\n", "import requests\n", "\n", "# Note: These examples assume the Flask API is running on localhost:5000\n", "API_BASE_URL = \"http://localhost:5000\"\n", "\n", "def test_api_endpoint(endpoint, method='GET', data=None):\n", "    \"\"\"Test API endpoint (will fail if server not running)\"\"\"\n", "    try:\n", "        url = f\"{API_BASE_URL}{endpoint}\"\n", "        if method == 'GET':\n", "            response = requests.get(url, timeout=5)\n", "        else:\n", "            response = requests.post(url, json=data, timeout=5)\n", "        \n", "        print(f\"Status: {response.status_code}\")\n", "        print(f\"Response: {response.json()}\")\n", "        return response.json()\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"API call failed: {e}\")\n", "        print(\"Make sure the Flask API server is running: python ml/inference/predict_wrapper.py\")\n", "        return None\n", "\n", "# Test health endpoint\n", "print(\"Testing health endpoint:\")\n", "test_api_endpoint(\"/health\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test prediction endpoint\n", "print(\"Testing prediction endpoint with normal data:\")\n", "test_api_endpoint(\"/predict\", method='POST', data=normal_data)\n", "\n", "print(\"\\nTesting prediction endpoint with fire scenario:\")\n", "test_api_endpoint(\"/predict\", method='POST', data=fire_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test sample prediction endpoints\n", "print(\"Testing sample prediction endpoint:\")\n", "test_api_endpoint(\"/predict/sample\")\n", "\n", "print(\"\\nTesting fire scenario endpoint:\")\n", "test_api_endpoint(\"/predict/fire-scenario\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Stream Integration {#stream-integration}\n", "\n", "Example of how the ML model integrates with stream processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Stream integration example\n", "print(\"Stream ML Integration Example:\")\n", "print(\"\\nTo run real-time ML predictions on streaming data:\")\n", "print(\"1. <PERSON> Ka<PERSON> and the data producer:\")\n", "print(\"   docker-compose up -d kafka smoke_kafka_producer\")\n", "print(\"\\n2. Run the stream ML processor:\")\n", "print(\"   python ml/inference/stream_ml_integration.py --model path/to/model.pkl\")\n", "print(\"\\n3. Monitor predictions:\")\n", "print(\"   python ml/inference/stream_ml_integration.py --monitor\")\n", "\n", "# Simulate stream processing\n", "print(\"\\nSimulating stream processing with sample data:\")\n", "stream_samples = [normal_data, fire_data, normal_data]\n", "\n", "for i, sample in enumerate(stream_samples):\n", "    prediction = predictor.predict_single(sample)\n", "    print(f\"\\nStream Sample {i+1}:\")\n", "    print(f\"  Prediction: {prediction['prediction_label']}\")\n", "    print(f\"  Confidence: {prediction.get('confidence', 'N/A')}\")\n", "    if prediction.get('prediction') == 1:\n", "        print(f\"  🚨 FIRE ALERT! 🚨\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated the complete ML workflow for IoT smoke detection:\n", "\n", "### Key Achievements:\n", "1. **Data Exploration**: Analyzed sensor data and target distribution\n", "2. **Feature Engineering**: Created advanced features to improve model performance\n", "3. **Model Training**: Trained and compared RandomForest and LogisticRegression models\n", "4. **Model Evaluation**: Comprehensive evaluation with metrics and visualizations\n", "5. **Prediction System**: Demonstrated single and batch predictions\n", "6. **API Integration**: Showed how to use the Flask API for real-time predictions\n", "7. **Stream Processing**: Explained integration with Kafka for live inference\n", "\n", "### Next Steps:\n", "- Deploy the Flask API for production use\n", "- Set up continuous model monitoring and retraining\n", "- Integrate with alerting systems for fire detection\n", "- Optimize model performance with more advanced techniques\n", "\n", "### API Endpoints:\n", "- `GET /health` - Health check\n", "- `GET /model/info` - Model information\n", "- `POST /predict` - Single prediction\n", "- `POST /predict/batch` - Batch predictions\n", "- `GET /predict/sample` - Test with sample data\n", "- `GET /predict/fire-scenario` - Test with fire scenario\n", "- `POST /validate` - Validate input data\n", "\n", "### Command Line Tools:\n", "- `python ml/training/train_model.py` - Train new models\n", "- `python ml/inference/predict.py --sample` - Test predictions\n", "- `python ml/inference/predict_wrapper.py` - Start Flask API\n", "- `python ml/inference/batch_inference.py --input data.csv` - Batch processing\n", "- `python ml/inference/stream_ml_integration.py` - Stream processing"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}