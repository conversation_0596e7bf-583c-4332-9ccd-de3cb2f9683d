# Test Dependencies for IoT Smoke Detection Data Pipeline
# Install with: pip install -r tests/requirements-test.txt

# Core testing framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-xdist>=3.3.1
pytest-mock>=3.11.1
pytest-asyncio>=0.21.1

# Test utilities
pytest-html>=3.2.0
pytest-json-report>=1.5.0
pytest-benchmark>=4.0.0
pytest-timeout>=2.1.0
pytest-randomly>=3.12.0

# Data science testing
pytest-datadir>=1.4.1
hypothesis>=6.82.0

# Mock and fixtures
responses>=0.23.3
factory-boy>=3.3.0
freezegun>=1.2.2

# Performance testing
memory-profiler>=0.61.0
psutil>=5.9.5

# Code quality
flake8>=6.0.0
black>=23.7.0
isort>=5.12.0
mypy>=1.5.0

# Coverage reporting
coverage[toml]>=7.2.0

# Documentation testing
# doctest  # Built-in Python module, no need to install

# Kafka testing (if needed)
kafka-python>=2.0.2
# confluent-kafka>=2.2.0  # Optional, can cause conflicts

# ML testing utilities
scikit-learn>=1.3.0
pandas>=2.0.0
numpy>=1.24.0

# API testing
flask>=2.3.0
requests>=2.31.0

# Stream processing testing
# pyspark>=3.4.0  # Optional, for Spark testing - commented out due to size

# Jupyter notebook testing
nbval>=0.10.0  # For testing notebooks

# Database testing (if needed)
sqlalchemy>=2.0.0
# sqlite3  # Built-in Python module, no need to install

# Environment and configuration testing
python-dotenv>=1.0.0
pydantic>=2.0.0

# Logging testing
structlog>=23.1.0

# Date/time testing
python-dateutil>=2.8.2

# File system testing
# pathlib2>=2.3.7  # Not needed for Python 3.10+

# Network testing
httpretty>=1.1.4

# Parallel testing
# pytest-parallel>=0.1.1  # Can conflict with pytest-xdist

# Test data generation
faker>=19.3.0
mimesis>=11.1.0

# Assertion helpers
assertpy>=1.1

# Test reporting
allure-pytest>=2.13.2

# Security testing
bandit>=1.7.5
safety>=2.3.0

# Performance profiling
py-spy>=0.3.14
line-profiler>=4.1.0

# Memory leak detection
pympler>=0.9

# Test discovery and organization
pytest-testmon>=2.0.0
pytest-watch>=4.2.0

# Continuous integration helpers
pytest-github-actions-annotate-failures>=0.2.0

# Test data management
pytest-datafiles>=3.0.0

# Snapshot testing
syrupy>=4.0.0

# Property-based testing
hypothesis[numpy,pandas]>=6.82.0

# Test fixtures for web testing
pytest-flask>=1.2.0
pytest-httpserver>=1.0.8

# Database fixtures
pytest-postgresql>=5.0.0
pytest-redis>=3.0.2

# Time-based testing
time-machine>=2.12.0

# Test result analysis
pytest-clarity>=1.0.1
pytest-sugar>=0.9.7

# Test execution control
pytest-order>=1.1.0
pytest-dependency>=0.5.1

# Environment isolation
pytest-env>=0.8.2
pytest-virtualenv>=1.7.0

# Test data validation
cerberus>=1.3.4
jsonschema>=4.19.0

# Configuration testing
configparser>=5.3.0
toml>=0.10.2

# Logging capture and testing
pytest-logging>=2016.11.4
loguru>=0.7.0

# Test metrics and reporting
pytest-metadata>=3.0.0
pytest-instafail>=0.5.0

# Cross-platform testing
pytest-subprocess>=1.5.0
pytest-shell-utilities>=1.8.0
