"""
    pygments.lexers._php_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    This file loads the function names and their modules from the
    php webpage and generates itself.

    Run with `python -I` to regenerate.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

MODULES = {'APCu': ('apcu_add',
          'apcu_cache_info',
          'apcu_cas',
          'apcu_clear_cache',
          'apcu_dec',
          'apcu_delete',
          'apcu_enabled',
          'apcu_entry',
          'apcu_exists',
          'apcu_fetch',
          'apcu_inc',
          'apcu_key_info',
          'apcu_sma_info',
          'apcu_store'),
 'Aliases and deprecated Mysqli': ('mysqli_connect',
                                   'mysqli_execute',
                                   'mysqli_get_client_stats',
                                   'mysqli_get_links_stats',
                                   'mysqli_report'),
 'Apache': ('apache_child_terminate',
            'apache_get_modules',
            'apache_get_version',
            'apache_getenv',
            'apache_lookup_uri',
            'apache_note',
            'apache_request_headers',
            'apache_response_headers',
            'apache_setenv',
            'getallheaders',
            'virtual'),
 'Array': ('array_change_key_case',
           'array_chunk',
           'array_column',
           'array_combine',
           'array_count_values',
           'array_diff_assoc',
           'array_diff_key',
           'array_diff_uassoc',
           'array_diff_ukey',
           'array_diff',
           'array_fill_keys',
           'array_fill',
           'array_filter',
           'array_flip',
           'array_intersect_assoc',
           'array_intersect_key',
           'array_intersect_uassoc',
           'array_intersect_ukey',
           'array_intersect',
           'array_is_list',
           'array_key_exists',
           'array_key_first',
           'array_key_last',
           'array_keys',
           'array_map',
           'array_merge_recursive',
           'array_merge',
           'array_multisort',
           'array_pad',
           'array_pop',
           'array_product',
           'array_push',
           'array_rand',
           'array_reduce',
           'array_replace_recursive',
           'array_replace',
           'array_reverse',
           'array_search',
           'array_shift',
           'array_slice',
           'array_splice',
           'array_sum',
           'array_udiff_assoc',
           'array_udiff_uassoc',
           'array_udiff',
           'array_uintersect_assoc',
           'array_uintersect_uassoc',
           'array_uintersect',
           'array_unique',
           'array_unshift',
           'array_values',
           'array_walk_recursive',
           'array_walk',
           'array',
           'arsort',
           'asort',
           'compact',
           'count',
           'current',
           'each',
           'end',
           'extract',
           'in_array',
           'key_exists',
           'key',
           'krsort',
           'ksort',
           'list',
           'natcasesort',
           'natsort',
           'next',
           'pos',
           'prev',
           'range',
           'reset',
           'rsort',
           'shuffle',
           'sizeof',
           'sort',
           'uasort',
           'uksort',
           'usort'),
 'BC Math': ('bcadd',
             'bccomp',
             'bcdiv',
             'bcmod',
             'bcmul',
             'bcpow',
             'bcpowmod',
             'bcscale',
             'bcsqrt',
             'bcsub'),
 'Bzip2': ('bzclose',
           'bzcompress',
           'bzdecompress',
           'bzerrno',
           'bzerror',
           'bzerrstr',
           'bzflush',
           'bzopen',
           'bzread',
           'bzwrite'),
 'COM': ('com_create_guid',
         'com_event_sink',
         'com_get_active_object',
         'com_load_typelib',
         'com_message_pump',
         'com_print_typeinfo',
         'variant_abs',
         'variant_add',
         'variant_and',
         'variant_cast',
         'variant_cat',
         'variant_cmp',
         'variant_date_from_timestamp',
         'variant_date_to_timestamp',
         'variant_div',
         'variant_eqv',
         'variant_fix',
         'variant_get_type',
         'variant_idiv',
         'variant_imp',
         'variant_int',
         'variant_mod',
         'variant_mul',
         'variant_neg',
         'variant_not',
         'variant_or',
         'variant_pow',
         'variant_round',
         'variant_set_type',
         'variant_set',
         'variant_sub',
         'variant_xor'),
 'CSPRNG': ('random_bytes', 'random_int'),
 'CUBRID': ('cubrid_bind',
            'cubrid_close_prepare',
            'cubrid_close_request',
            'cubrid_col_get',
            'cubrid_col_size',
            'cubrid_column_names',
            'cubrid_column_types',
            'cubrid_commit',
            'cubrid_connect_with_url',
            'cubrid_connect',
            'cubrid_current_oid',
            'cubrid_disconnect',
            'cubrid_drop',
            'cubrid_error_code_facility',
            'cubrid_error_code',
            'cubrid_error_msg',
            'cubrid_execute',
            'cubrid_fetch',
            'cubrid_free_result',
            'cubrid_get_autocommit',
            'cubrid_get_charset',
            'cubrid_get_class_name',
            'cubrid_get_client_info',
            'cubrid_get_db_parameter',
            'cubrid_get_query_timeout',
            'cubrid_get_server_info',
            'cubrid_get',
            'cubrid_insert_id',
            'cubrid_is_instance',
            'cubrid_lob_close',
            'cubrid_lob_export',
            'cubrid_lob_get',
            'cubrid_lob_send',
            'cubrid_lob_size',
            'cubrid_lob2_bind',
            'cubrid_lob2_close',
            'cubrid_lob2_export',
            'cubrid_lob2_import',
            'cubrid_lob2_new',
            'cubrid_lob2_read',
            'cubrid_lob2_seek64',
            'cubrid_lob2_seek',
            'cubrid_lob2_size64',
            'cubrid_lob2_size',
            'cubrid_lob2_tell64',
            'cubrid_lob2_tell',
            'cubrid_lob2_write',
            'cubrid_lock_read',
            'cubrid_lock_write',
            'cubrid_move_cursor',
            'cubrid_next_result',
            'cubrid_num_cols',
            'cubrid_num_rows',
            'cubrid_pconnect_with_url',
            'cubrid_pconnect',
            'cubrid_prepare',
            'cubrid_put',
            'cubrid_rollback',
            'cubrid_schema',
            'cubrid_seq_drop',
            'cubrid_seq_insert',
            'cubrid_seq_put',
            'cubrid_set_add',
            'cubrid_set_autocommit',
            'cubrid_set_db_parameter',
            'cubrid_set_drop',
            'cubrid_set_query_timeout',
            'cubrid_version'),
 'Calendar': ('cal_days_in_month',
              'cal_from_jd',
              'cal_info',
              'cal_to_jd',
              'easter_date',
              'easter_days',
              'frenchtojd',
              'gregoriantojd',
              'jddayofweek',
              'jdmonthname',
              'jdtofrench',
              'jdtogregorian',
              'jdtojewish',
              'jdtojulian',
              'jdtounix',
              'jewishtojd',
              'juliantojd',
              'unixtojd'),
 'Classes/Object': ('__autoload',
                    'class_alias',
                    'class_exists',
                    'enum_exists',
                    'get_called_class',
                    'get_class_methods',
                    'get_class_vars',
                    'get_class',
                    'get_declared_classes',
                    'get_declared_interfaces',
                    'get_declared_traits',
                    'get_mangled_object_vars',
                    'get_object_vars',
                    'get_parent_class',
                    'interface_exists',
                    'is_a',
                    'is_subclass_of',
                    'method_exists',
                    'property_exists',
                    'trait_exists'),
 'Ctype': ('ctype_alnum',
           'ctype_alpha',
           'ctype_cntrl',
           'ctype_digit',
           'ctype_graph',
           'ctype_lower',
           'ctype_print',
           'ctype_punct',
           'ctype_space',
           'ctype_upper',
           'ctype_xdigit'),
 'DBA': ('dba_close',
         'dba_delete',
         'dba_exists',
         'dba_fetch',
         'dba_firstkey',
         'dba_handlers',
         'dba_insert',
         'dba_key_split',
         'dba_list',
         'dba_nextkey',
         'dba_open',
         'dba_optimize',
         'dba_popen',
         'dba_replace',
         'dba_sync'),
 'DOM': ('dom_import_simplexml',),
 'Date/Time': ('checkdate',
               'date_add',
               'date_create_from_format',
               'date_create_immutable_from_format',
               'date_create_immutable',
               'date_create',
               'date_date_set',
               'date_default_timezone_get',
               'date_default_timezone_set',
               'date_diff',
               'date_format',
               'date_get_last_errors',
               'date_interval_create_from_date_string',
               'date_interval_format',
               'date_isodate_set',
               'date_modify',
               'date_offset_get',
               'date_parse_from_format',
               'date_parse',
               'date_sub',
               'date_sun_info',
               'date_sunrise',
               'date_sunset',
               'date_time_set',
               'date_timestamp_get',
               'date_timestamp_set',
               'date_timezone_get',
               'date_timezone_set',
               'date',
               'getdate',
               'gettimeofday',
               'gmdate',
               'gmmktime',
               'gmstrftime',
               'idate',
               'localtime',
               'microtime',
               'mktime',
               'strftime',
               'strptime',
               'strtotime',
               'time',
               'timezone_abbreviations_list',
               'timezone_identifiers_list',
               'timezone_location_get',
               'timezone_name_from_abbr',
               'timezone_name_get',
               'timezone_offset_get',
               'timezone_open',
               'timezone_transitions_get',
               'timezone_version_get'),
 'Direct IO': ('dio_close',
               'dio_fcntl',
               'dio_open',
               'dio_read',
               'dio_seek',
               'dio_stat',
               'dio_tcsetattr',
               'dio_truncate',
               'dio_write'),
 'Directory': ('chdir',
               'chroot',
               'closedir',
               'dir',
               'getcwd',
               'opendir',
               'readdir',
               'rewinddir',
               'scandir'),
 'Eio': ('eio_busy',
         'eio_cancel',
         'eio_chmod',
         'eio_chown',
         'eio_close',
         'eio_custom',
         'eio_dup2',
         'eio_event_loop',
         'eio_fallocate',
         'eio_fchmod',
         'eio_fchown',
         'eio_fdatasync',
         'eio_fstat',
         'eio_fstatvfs',
         'eio_fsync',
         'eio_ftruncate',
         'eio_futime',
         'eio_get_event_stream',
         'eio_get_last_error',
         'eio_grp_add',
         'eio_grp_cancel',
         'eio_grp_limit',
         'eio_grp',
         'eio_init',
         'eio_link',
         'eio_lstat',
         'eio_mkdir',
         'eio_mknod',
         'eio_nop',
         'eio_npending',
         'eio_nready',
         'eio_nreqs',
         'eio_nthreads',
         'eio_open',
         'eio_poll',
         'eio_read',
         'eio_readahead',
         'eio_readdir',
         'eio_readlink',
         'eio_realpath',
         'eio_rename',
         'eio_rmdir',
         'eio_seek',
         'eio_sendfile',
         'eio_set_max_idle',
         'eio_set_max_parallel',
         'eio_set_max_poll_reqs',
         'eio_set_max_poll_time',
         'eio_set_min_parallel',
         'eio_stat',
         'eio_statvfs',
         'eio_symlink',
         'eio_sync_file_range',
         'eio_sync',
         'eio_syncfs',
         'eio_truncate',
         'eio_unlink',
         'eio_utime',
         'eio_write'),
 'Enchant': ('enchant_broker_describe',
             'enchant_broker_dict_exists',
             'enchant_broker_free_dict',
             'enchant_broker_free',
             'enchant_broker_get_dict_path',
             'enchant_broker_get_error',
             'enchant_broker_init',
             'enchant_broker_list_dicts',
             'enchant_broker_request_dict',
             'enchant_broker_request_pwl_dict',
             'enchant_broker_set_dict_path',
             'enchant_broker_set_ordering',
             'enchant_dict_add_to_personal',
             'enchant_dict_add_to_session',
             'enchant_dict_add',
             'enchant_dict_check',
             'enchant_dict_describe',
             'enchant_dict_get_error',
             'enchant_dict_is_added',
             'enchant_dict_is_in_session',
             'enchant_dict_quick_check',
             'enchant_dict_store_replacement',
             'enchant_dict_suggest'),
 'Error Handling': ('debug_backtrace',
                    'debug_print_backtrace',
                    'error_clear_last',
                    'error_get_last',
                    'error_log',
                    'error_reporting',
                    'restore_error_handler',
                    'restore_exception_handler',
                    'set_error_handler',
                    'set_exception_handler',
                    'trigger_error',
                    'user_error'),
 'Exif': ('exif_imagetype',
          'exif_read_data',
          'exif_tagname',
          'exif_thumbnail',
          'read_exif_data'),
 'Expect': ('expect_expectl', 'expect_popen'),
 'FDF': ('fdf_add_doc_javascript',
         'fdf_add_template',
         'fdf_close',
         'fdf_create',
         'fdf_enum_values',
         'fdf_errno',
         'fdf_error',
         'fdf_get_ap',
         'fdf_get_attachment',
         'fdf_get_encoding',
         'fdf_get_file',
         'fdf_get_flags',
         'fdf_get_opt',
         'fdf_get_status',
         'fdf_get_value',
         'fdf_get_version',
         'fdf_header',
         'fdf_next_field_name',
         'fdf_open_string',
         'fdf_open',
         'fdf_remove_item',
         'fdf_save_string',
         'fdf_save',
         'fdf_set_ap',
         'fdf_set_encoding',
         'fdf_set_file',
         'fdf_set_flags',
         'fdf_set_javascript_action',
         'fdf_set_on_import_javascript',
         'fdf_set_opt',
         'fdf_set_status',
         'fdf_set_submit_form_action',
         'fdf_set_target_frame',
         'fdf_set_value',
         'fdf_set_version'),
 'FPM': ('fastcgi_finish_request',),
 'FTP': ('ftp_alloc',
         'ftp_append',
         'ftp_cdup',
         'ftp_chdir',
         'ftp_chmod',
         'ftp_close',
         'ftp_connect',
         'ftp_delete',
         'ftp_exec',
         'ftp_fget',
         'ftp_fput',
         'ftp_get_option',
         'ftp_get',
         'ftp_login',
         'ftp_mdtm',
         'ftp_mkdir',
         'ftp_mlsd',
         'ftp_nb_continue',
         'ftp_nb_fget',
         'ftp_nb_fput',
         'ftp_nb_get',
         'ftp_nb_put',
         'ftp_nlist',
         'ftp_pasv',
         'ftp_put',
         'ftp_pwd',
         'ftp_quit',
         'ftp_raw',
         'ftp_rawlist',
         'ftp_rename',
         'ftp_rmdir',
         'ftp_set_option',
         'ftp_site',
         'ftp_size',
         'ftp_ssl_connect',
         'ftp_systype'),
 'Fann': ('fann_cascadetrain_on_data',
          'fann_cascadetrain_on_file',
          'fann_clear_scaling_params',
          'fann_copy',
          'fann_create_from_file',
          'fann_create_shortcut_array',
          'fann_create_shortcut',
          'fann_create_sparse_array',
          'fann_create_sparse',
          'fann_create_standard_array',
          'fann_create_standard',
          'fann_create_train_from_callback',
          'fann_create_train',
          'fann_descale_input',
          'fann_descale_output',
          'fann_descale_train',
          'fann_destroy_train',
          'fann_destroy',
          'fann_duplicate_train_data',
          'fann_get_activation_function',
          'fann_get_activation_steepness',
          'fann_get_bias_array',
          'fann_get_bit_fail_limit',
          'fann_get_bit_fail',
          'fann_get_cascade_activation_functions_count',
          'fann_get_cascade_activation_functions',
          'fann_get_cascade_activation_steepnesses_count',
          'fann_get_cascade_activation_steepnesses',
          'fann_get_cascade_candidate_change_fraction',
          'fann_get_cascade_candidate_limit',
          'fann_get_cascade_candidate_stagnation_epochs',
          'fann_get_cascade_max_cand_epochs',
          'fann_get_cascade_max_out_epochs',
          'fann_get_cascade_min_cand_epochs',
          'fann_get_cascade_min_out_epochs',
          'fann_get_cascade_num_candidate_groups',
          'fann_get_cascade_num_candidates',
          'fann_get_cascade_output_change_fraction',
          'fann_get_cascade_output_stagnation_epochs',
          'fann_get_cascade_weight_multiplier',
          'fann_get_connection_array',
          'fann_get_connection_rate',
          'fann_get_errno',
          'fann_get_errstr',
          'fann_get_layer_array',
          'fann_get_learning_momentum',
          'fann_get_learning_rate',
          'fann_get_MSE',
          'fann_get_network_type',
          'fann_get_num_input',
          'fann_get_num_layers',
          'fann_get_num_output',
          'fann_get_quickprop_decay',
          'fann_get_quickprop_mu',
          'fann_get_rprop_decrease_factor',
          'fann_get_rprop_delta_max',
          'fann_get_rprop_delta_min',
          'fann_get_rprop_delta_zero',
          'fann_get_rprop_increase_factor',
          'fann_get_sarprop_step_error_shift',
          'fann_get_sarprop_step_error_threshold_factor',
          'fann_get_sarprop_temperature',
          'fann_get_sarprop_weight_decay_shift',
          'fann_get_total_connections',
          'fann_get_total_neurons',
          'fann_get_train_error_function',
          'fann_get_train_stop_function',
          'fann_get_training_algorithm',
          'fann_init_weights',
          'fann_length_train_data',
          'fann_merge_train_data',
          'fann_num_input_train_data',
          'fann_num_output_train_data',
          'fann_print_error',
          'fann_randomize_weights',
          'fann_read_train_from_file',
          'fann_reset_errno',
          'fann_reset_errstr',
          'fann_reset_MSE',
          'fann_run',
          'fann_save_train',
          'fann_save',
          'fann_scale_input_train_data',
          'fann_scale_input',
          'fann_scale_output_train_data',
          'fann_scale_output',
          'fann_scale_train_data',
          'fann_scale_train',
          'fann_set_activation_function_hidden',
          'fann_set_activation_function_layer',
          'fann_set_activation_function_output',
          'fann_set_activation_function',
          'fann_set_activation_steepness_hidden',
          'fann_set_activation_steepness_layer',
          'fann_set_activation_steepness_output',
          'fann_set_activation_steepness',
          'fann_set_bit_fail_limit',
          'fann_set_callback',
          'fann_set_cascade_activation_functions',
          'fann_set_cascade_activation_steepnesses',
          'fann_set_cascade_candidate_change_fraction',
          'fann_set_cascade_candidate_limit',
          'fann_set_cascade_candidate_stagnation_epochs',
          'fann_set_cascade_max_cand_epochs',
          'fann_set_cascade_max_out_epochs',
          'fann_set_cascade_min_cand_epochs',
          'fann_set_cascade_min_out_epochs',
          'fann_set_cascade_num_candidate_groups',
          'fann_set_cascade_output_change_fraction',
          'fann_set_cascade_output_stagnation_epochs',
          'fann_set_cascade_weight_multiplier',
          'fann_set_error_log',
          'fann_set_input_scaling_params',
          'fann_set_learning_momentum',
          'fann_set_learning_rate',
          'fann_set_output_scaling_params',
          'fann_set_quickprop_decay',
          'fann_set_quickprop_mu',
          'fann_set_rprop_decrease_factor',
          'fann_set_rprop_delta_max',
          'fann_set_rprop_delta_min',
          'fann_set_rprop_delta_zero',
          'fann_set_rprop_increase_factor',
          'fann_set_sarprop_step_error_shift',
          'fann_set_sarprop_step_error_threshold_factor',
          'fann_set_sarprop_temperature',
          'fann_set_sarprop_weight_decay_shift',
          'fann_set_scaling_params',
          'fann_set_train_error_function',
          'fann_set_train_stop_function',
          'fann_set_training_algorithm',
          'fann_set_weight_array',
          'fann_set_weight',
          'fann_shuffle_train_data',
          'fann_subset_train_data',
          'fann_test_data',
          'fann_test',
          'fann_train_epoch',
          'fann_train_on_data',
          'fann_train_on_file',
          'fann_train'),
 'Fileinfo': ('finfo_buffer',
              'finfo_close',
              'finfo_file',
              'finfo_open',
              'finfo_set_flags',
              'mime_content_type'),
 'Filesystem': ('basename',
                'chgrp',
                'chmod',
                'chown',
                'clearstatcache',
                'copy',
                'dirname',
                'disk_free_space',
                'disk_total_space',
                'diskfreespace',
                'fclose',
                'fdatasync',
                'feof',
                'fflush',
                'fgetc',
                'fgetcsv',
                'fgets',
                'fgetss',
                'file_exists',
                'file_get_contents',
                'file_put_contents',
                'file',
                'fileatime',
                'filectime',
                'filegroup',
                'fileinode',
                'filemtime',
                'fileowner',
                'fileperms',
                'filesize',
                'filetype',
                'flock',
                'fnmatch',
                'fopen',
                'fpassthru',
                'fputcsv',
                'fputs',
                'fread',
                'fscanf',
                'fseek',
                'fstat',
                'fsync',
                'ftell',
                'ftruncate',
                'fwrite',
                'glob',
                'is_dir',
                'is_executable',
                'is_file',
                'is_link',
                'is_readable',
                'is_uploaded_file',
                'is_writable',
                'is_writeable',
                'lchgrp',
                'lchown',
                'link',
                'linkinfo',
                'lstat',
                'mkdir',
                'move_uploaded_file',
                'parse_ini_file',
                'parse_ini_string',
                'pathinfo',
                'pclose',
                'popen',
                'readfile',
                'readlink',
                'realpath_cache_get',
                'realpath_cache_size',
                'realpath',
                'rename',
                'rewind',
                'rmdir',
                'set_file_buffer',
                'stat',
                'symlink',
                'tempnam',
                'tmpfile',
                'touch',
                'umask',
                'unlink'),
 'Filter': ('filter_has_var',
            'filter_id',
            'filter_input_array',
            'filter_input',
            'filter_list',
            'filter_var_array',
            'filter_var'),
 'Firebird/InterBase': ('fbird_add_user',
                        'fbird_affected_rows',
                        'fbird_backup',
                        'fbird_blob_add',
                        'fbird_blob_cancel',
                        'fbird_blob_close',
                        'fbird_blob_create',
                        'fbird_blob_echo',
                        'fbird_blob_get',
                        'fbird_blob_import',
                        'fbird_blob_info',
                        'fbird_blob_open',
                        'fbird_close',
                        'fbird_commit_ret',
                        'fbird_commit',
                        'fbird_connect',
                        'fbird_db_info',
                        'fbird_delete_user',
                        'fbird_drop_db',
                        'fbird_errcode',
                        'fbird_errmsg',
                        'fbird_execute',
                        'fbird_fetch_assoc',
                        'fbird_fetch_object',
                        'fbird_fetch_row',
                        'fbird_field_info',
                        'fbird_free_event_handler',
                        'fbird_free_query',
                        'fbird_free_result',
                        'fbird_gen_id',
                        'fbird_maintain_db',
                        'fbird_modify_user',
                        'fbird_name_result',
                        'fbird_num_fields',
                        'fbird_num_params',
                        'fbird_param_info',
                        'fbird_pconnect',
                        'fbird_prepare',
                        'fbird_query',
                        'fbird_restore',
                        'fbird_rollback_ret',
                        'fbird_rollback',
                        'fbird_server_info',
                        'fbird_service_attach',
                        'fbird_service_detach',
                        'fbird_set_event_handler',
                        'fbird_trans',
                        'fbird_wait_event',
                        'ibase_add_user',
                        'ibase_affected_rows',
                        'ibase_backup',
                        'ibase_blob_add',
                        'ibase_blob_cancel',
                        'ibase_blob_close',
                        'ibase_blob_create',
                        'ibase_blob_echo',
                        'ibase_blob_get',
                        'ibase_blob_import',
                        'ibase_blob_info',
                        'ibase_blob_open',
                        'ibase_close',
                        'ibase_commit_ret',
                        'ibase_commit',
                        'ibase_connect',
                        'ibase_db_info',
                        'ibase_delete_user',
                        'ibase_drop_db',
                        'ibase_errcode',
                        'ibase_errmsg',
                        'ibase_execute',
                        'ibase_fetch_assoc',
                        'ibase_fetch_object',
                        'ibase_fetch_row',
                        'ibase_field_info',
                        'ibase_free_event_handler',
                        'ibase_free_query',
                        'ibase_free_result',
                        'ibase_gen_id',
                        'ibase_maintain_db',
                        'ibase_modify_user',
                        'ibase_name_result',
                        'ibase_num_fields',
                        'ibase_num_params',
                        'ibase_param_info',
                        'ibase_pconnect',
                        'ibase_prepare',
                        'ibase_query',
                        'ibase_restore',
                        'ibase_rollback_ret',
                        'ibase_rollback',
                        'ibase_server_info',
                        'ibase_service_attach',
                        'ibase_service_detach',
                        'ibase_set_event_handler',
                        'ibase_trans',
                        'ibase_wait_event'),
 'Function handling': ('call_user_func_array',
                       'call_user_func',
                       'create_function',
                       'forward_static_call_array',
                       'forward_static_call',
                       'func_get_arg',
                       'func_get_args',
                       'func_num_args',
                       'function_exists',
                       'get_defined_functions',
                       'register_shutdown_function',
                       'register_tick_function',
                       'unregister_tick_function'),
 'GD and Image': ('gd_info',
                  'getimagesize',
                  'getimagesizefromstring',
                  'image_type_to_extension',
                  'image_type_to_mime_type',
                  'image2wbmp',
                  'imageaffine',
                  'imageaffinematrixconcat',
                  'imageaffinematrixget',
                  'imagealphablending',
                  'imageantialias',
                  'imagearc',
                  'imageavif',
                  'imagebmp',
                  'imagechar',
                  'imagecharup',
                  'imagecolorallocate',
                  'imagecolorallocatealpha',
                  'imagecolorat',
                  'imagecolorclosest',
                  'imagecolorclosestalpha',
                  'imagecolorclosesthwb',
                  'imagecolordeallocate',
                  'imagecolorexact',
                  'imagecolorexactalpha',
                  'imagecolormatch',
                  'imagecolorresolve',
                  'imagecolorresolvealpha',
                  'imagecolorset',
                  'imagecolorsforindex',
                  'imagecolorstotal',
                  'imagecolortransparent',
                  'imageconvolution',
                  'imagecopy',
                  'imagecopymerge',
                  'imagecopymergegray',
                  'imagecopyresampled',
                  'imagecopyresized',
                  'imagecreate',
                  'imagecreatefromavif',
                  'imagecreatefrombmp',
                  'imagecreatefromgd2',
                  'imagecreatefromgd2part',
                  'imagecreatefromgd',
                  'imagecreatefromgif',
                  'imagecreatefromjpeg',
                  'imagecreatefrompng',
                  'imagecreatefromstring',
                  'imagecreatefromtga',
                  'imagecreatefromwbmp',
                  'imagecreatefromwebp',
                  'imagecreatefromxbm',
                  'imagecreatefromxpm',
                  'imagecreatetruecolor',
                  'imagecrop',
                  'imagecropauto',
                  'imagedashedline',
                  'imagedestroy',
                  'imageellipse',
                  'imagefill',
                  'imagefilledarc',
                  'imagefilledellipse',
                  'imagefilledpolygon',
                  'imagefilledrectangle',
                  'imagefilltoborder',
                  'imagefilter',
                  'imageflip',
                  'imagefontheight',
                  'imagefontwidth',
                  'imageftbbox',
                  'imagefttext',
                  'imagegammacorrect',
                  'imagegd2',
                  'imagegd',
                  'imagegetclip',
                  'imagegetinterpolation',
                  'imagegif',
                  'imagegrabscreen',
                  'imagegrabwindow',
                  'imageinterlace',
                  'imageistruecolor',
                  'imagejpeg',
                  'imagelayereffect',
                  'imageline',
                  'imageloadfont',
                  'imageopenpolygon',
                  'imagepalettecopy',
                  'imagepalettetotruecolor',
                  'imagepng',
                  'imagepolygon',
                  'imagerectangle',
                  'imageresolution',
                  'imagerotate',
                  'imagesavealpha',
                  'imagescale',
                  'imagesetbrush',
                  'imagesetclip',
                  'imagesetinterpolation',
                  'imagesetpixel',
                  'imagesetstyle',
                  'imagesetthickness',
                  'imagesettile',
                  'imagestring',
                  'imagestringup',
                  'imagesx',
                  'imagesy',
                  'imagetruecolortopalette',
                  'imagettfbbox',
                  'imagettftext',
                  'imagetypes',
                  'imagewbmp',
                  'imagewebp',
                  'imagexbm',
                  'iptcembed',
                  'iptcparse',
                  'jpeg2wbmp',
                  'png2wbmp'),
 'GMP': ('gmp_abs',
         'gmp_add',
         'gmp_and',
         'gmp_binomial',
         'gmp_clrbit',
         'gmp_cmp',
         'gmp_com',
         'gmp_div_q',
         'gmp_div_qr',
         'gmp_div_r',
         'gmp_div',
         'gmp_divexact',
         'gmp_export',
         'gmp_fact',
         'gmp_gcd',
         'gmp_gcdext',
         'gmp_hamdist',
         'gmp_import',
         'gmp_init',
         'gmp_intval',
         'gmp_invert',
         'gmp_jacobi',
         'gmp_kronecker',
         'gmp_lcm',
         'gmp_legendre',
         'gmp_mod',
         'gmp_mul',
         'gmp_neg',
         'gmp_nextprime',
         'gmp_or',
         'gmp_perfect_power',
         'gmp_perfect_square',
         'gmp_popcount',
         'gmp_pow',
         'gmp_powm',
         'gmp_prob_prime',
         'gmp_random_bits',
         'gmp_random_range',
         'gmp_random_seed',
         'gmp_random',
         'gmp_root',
         'gmp_rootrem',
         'gmp_scan0',
         'gmp_scan1',
         'gmp_setbit',
         'gmp_sign',
         'gmp_sqrt',
         'gmp_sqrtrem',
         'gmp_strval',
         'gmp_sub',
         'gmp_testbit',
         'gmp_xor'),
 'GeoIP': ('geoip_asnum_by_name',
           'geoip_continent_code_by_name',
           'geoip_country_code_by_name',
           'geoip_country_code3_by_name',
           'geoip_country_name_by_name',
           'geoip_database_info',
           'geoip_db_avail',
           'geoip_db_filename',
           'geoip_db_get_all_info',
           'geoip_domain_by_name',
           'geoip_id_by_name',
           'geoip_isp_by_name',
           'geoip_netspeedcell_by_name',
           'geoip_org_by_name',
           'geoip_record_by_name',
           'geoip_region_by_name',
           'geoip_region_name_by_code',
           'geoip_setup_custom_directory',
           'geoip_time_zone_by_country_and_region'),
 'Gettext': ('bind_textdomain_codeset',
             'bindtextdomain',
             'dcgettext',
             'dcngettext',
             'dgettext',
             'dngettext',
             'gettext',
             'ngettext',
             'textdomain'),
 'GnuPG': ('gnupg_adddecryptkey',
           'gnupg_addencryptkey',
           'gnupg_addsignkey',
           'gnupg_cleardecryptkeys',
           'gnupg_clearencryptkeys',
           'gnupg_clearsignkeys',
           'gnupg_decrypt',
           'gnupg_decryptverify',
           'gnupg_encrypt',
           'gnupg_encryptsign',
           'gnupg_export',
           'gnupg_getengineinfo',
           'gnupg_geterror',
           'gnupg_geterrorinfo',
           'gnupg_getprotocol',
           'gnupg_import',
           'gnupg_init',
           'gnupg_keyinfo',
           'gnupg_setarmor',
           'gnupg_seterrormode',
           'gnupg_setsignmode',
           'gnupg_sign',
           'gnupg_verify'),
 'Grapheme': ('grapheme_extract',
              'grapheme_stripos',
              'grapheme_stristr',
              'grapheme_strlen',
              'grapheme_strpos',
              'grapheme_strripos',
              'grapheme_strrpos',
              'grapheme_strstr',
              'grapheme_substr'),
 'Hash': ('hash_algos',
          'hash_copy',
          'hash_equals',
          'hash_file',
          'hash_final',
          'hash_hkdf',
          'hash_hmac_algos',
          'hash_hmac_file',
          'hash_hmac',
          'hash_init',
          'hash_pbkdf2',
          'hash_update_file',
          'hash_update_stream',
          'hash_update',
          'hash'),
 'IBM DB2': ('db2_autocommit',
             'db2_bind_param',
             'db2_client_info',
             'db2_close',
             'db2_column_privileges',
             'db2_columns',
             'db2_commit',
             'db2_conn_error',
             'db2_conn_errormsg',
             'db2_connect',
             'db2_cursor_type',
             'db2_escape_string',
             'db2_exec',
             'db2_execute',
             'db2_fetch_array',
             'db2_fetch_assoc',
             'db2_fetch_both',
             'db2_fetch_object',
             'db2_fetch_row',
             'db2_field_display_size',
             'db2_field_name',
             'db2_field_num',
             'db2_field_precision',
             'db2_field_scale',
             'db2_field_type',
             'db2_field_width',
             'db2_foreign_keys',
             'db2_free_result',
             'db2_free_stmt',
             'db2_get_option',
             'db2_last_insert_id',
             'db2_lob_read',
             'db2_next_result',
             'db2_num_fields',
             'db2_num_rows',
             'db2_pclose',
             'db2_pconnect',
             'db2_prepare',
             'db2_primary_keys',
             'db2_procedure_columns',
             'db2_procedures',
             'db2_result',
             'db2_rollback',
             'db2_server_info',
             'db2_set_option',
             'db2_special_columns',
             'db2_statistics',
             'db2_stmt_error',
             'db2_stmt_errormsg',
             'db2_table_privileges',
             'db2_tables'),
 'IDN': ('idn_to_ascii', 'idn_to_utf8'),
 'IMAP': ('imap_8bit',
          'imap_alerts',
          'imap_append',
          'imap_base64',
          'imap_binary',
          'imap_body',
          'imap_bodystruct',
          'imap_check',
          'imap_clearflag_full',
          'imap_close',
          'imap_create',
          'imap_createmailbox',
          'imap_delete',
          'imap_deletemailbox',
          'imap_errors',
          'imap_expunge',
          'imap_fetch_overview',
          'imap_fetchbody',
          'imap_fetchheader',
          'imap_fetchmime',
          'imap_fetchstructure',
          'imap_fetchtext',
          'imap_gc',
          'imap_get_quota',
          'imap_get_quotaroot',
          'imap_getacl',
          'imap_getmailboxes',
          'imap_getsubscribed',
          'imap_header',
          'imap_headerinfo',
          'imap_headers',
          'imap_last_error',
          'imap_list',
          'imap_listmailbox',
          'imap_listscan',
          'imap_listsubscribed',
          'imap_lsub',
          'imap_mail_compose',
          'imap_mail_copy',
          'imap_mail_move',
          'imap_mail',
          'imap_mailboxmsginfo',
          'imap_mime_header_decode',
          'imap_msgno',
          'imap_mutf7_to_utf8',
          'imap_num_msg',
          'imap_num_recent',
          'imap_open',
          'imap_ping',
          'imap_qprint',
          'imap_rename',
          'imap_renamemailbox',
          'imap_reopen',
          'imap_rfc822_parse_adrlist',
          'imap_rfc822_parse_headers',
          'imap_rfc822_write_address',
          'imap_savebody',
          'imap_scan',
          'imap_scanmailbox',
          'imap_search',
          'imap_set_quota',
          'imap_setacl',
          'imap_setflag_full',
          'imap_sort',
          'imap_status',
          'imap_subscribe',
          'imap_thread',
          'imap_timeout',
          'imap_uid',
          'imap_undelete',
          'imap_unsubscribe',
          'imap_utf7_decode',
          'imap_utf7_encode',
          'imap_utf8_to_mutf7',
          'imap_utf8'),
 'Igbinary': ('igbinary_serialize', 'igbinary_unserialize'),
 'Inotify': ('inotify_add_watch',
             'inotify_init',
             'inotify_queue_len',
             'inotify_read',
             'inotify_rm_watch'),
 'JSON': ('json_decode',
          'json_encode',
          'json_last_error_msg',
          'json_last_error'),
 'LDAP': ('ldap_8859_to_t61',
          'ldap_add_ext',
          'ldap_add',
          'ldap_bind_ext',
          'ldap_bind',
          'ldap_close',
          'ldap_compare',
          'ldap_connect',
          'ldap_control_paged_result_response',
          'ldap_control_paged_result',
          'ldap_count_entries',
          'ldap_count_references',
          'ldap_delete_ext',
          'ldap_delete',
          'ldap_dn2ufn',
          'ldap_err2str',
          'ldap_errno',
          'ldap_error',
          'ldap_escape',
          'ldap_exop_passwd',
          'ldap_exop_refresh',
          'ldap_exop_whoami',
          'ldap_exop',
          'ldap_explode_dn',
          'ldap_first_attribute',
          'ldap_first_entry',
          'ldap_first_reference',
          'ldap_free_result',
          'ldap_get_attributes',
          'ldap_get_dn',
          'ldap_get_entries',
          'ldap_get_option',
          'ldap_get_values_len',
          'ldap_get_values',
          'ldap_list',
          'ldap_mod_add_ext',
          'ldap_mod_add',
          'ldap_mod_del_ext',
          'ldap_mod_del',
          'ldap_mod_replace_ext',
          'ldap_mod_replace',
          'ldap_modify_batch',
          'ldap_modify',
          'ldap_next_attribute',
          'ldap_next_entry',
          'ldap_next_reference',
          'ldap_parse_exop',
          'ldap_parse_reference',
          'ldap_parse_result',
          'ldap_read',
          'ldap_rename_ext',
          'ldap_rename',
          'ldap_sasl_bind',
          'ldap_search',
          'ldap_set_option',
          'ldap_set_rebind_proc',
          'ldap_sort',
          'ldap_start_tls',
          'ldap_t61_to_8859',
          'ldap_unbind'),
 'LZF': ('lzf_compress', 'lzf_decompress', 'lzf_optimized_for'),
 'Mail': ('ezmlm_hash', 'mail'),
 'Mailparse': ('mailparse_determine_best_xfer_encoding',
               'mailparse_msg_create',
               'mailparse_msg_extract_part_file',
               'mailparse_msg_extract_part',
               'mailparse_msg_extract_whole_part_file',
               'mailparse_msg_free',
               'mailparse_msg_get_part_data',
               'mailparse_msg_get_part',
               'mailparse_msg_get_structure',
               'mailparse_msg_parse_file',
               'mailparse_msg_parse',
               'mailparse_rfc822_parse_addresses',
               'mailparse_stream_encode',
               'mailparse_uudecode_all'),
 'Math': ('abs',
          'acos',
          'acosh',
          'asin',
          'asinh',
          'atan2',
          'atan',
          'atanh',
          'base_convert',
          'bindec',
          'ceil',
          'cos',
          'cosh',
          'decbin',
          'dechex',
          'decoct',
          'deg2rad',
          'exp',
          'expm1',
          'fdiv',
          'floor',
          'fmod',
          'getrandmax',
          'hexdec',
          'hypot',
          'intdiv',
          'is_finite',
          'is_infinite',
          'is_nan',
          'lcg_value',
          'log10',
          'log1p',
          'log',
          'max',
          'min',
          'mt_getrandmax',
          'mt_rand',
          'mt_srand',
          'octdec',
          'pi',
          'pow',
          'rad2deg',
          'rand',
          'round',
          'sin',
          'sinh',
          'sqrt',
          'srand',
          'tan',
          'tanh'),
 'Mcrypt': ('mcrypt_create_iv',
            'mcrypt_decrypt',
            'mcrypt_enc_get_algorithms_name',
            'mcrypt_enc_get_block_size',
            'mcrypt_enc_get_iv_size',
            'mcrypt_enc_get_key_size',
            'mcrypt_enc_get_modes_name',
            'mcrypt_enc_get_supported_key_sizes',
            'mcrypt_enc_is_block_algorithm_mode',
            'mcrypt_enc_is_block_algorithm',
            'mcrypt_enc_is_block_mode',
            'mcrypt_enc_self_test',
            'mcrypt_encrypt',
            'mcrypt_generic_deinit',
            'mcrypt_generic_init',
            'mcrypt_generic',
            'mcrypt_get_block_size',
            'mcrypt_get_cipher_name',
            'mcrypt_get_iv_size',
            'mcrypt_get_key_size',
            'mcrypt_list_algorithms',
            'mcrypt_list_modes',
            'mcrypt_module_close',
            'mcrypt_module_get_algo_block_size',
            'mcrypt_module_get_algo_key_size',
            'mcrypt_module_get_supported_key_sizes',
            'mcrypt_module_is_block_algorithm_mode',
            'mcrypt_module_is_block_algorithm',
            'mcrypt_module_is_block_mode',
            'mcrypt_module_open',
            'mcrypt_module_self_test',
            'mdecrypt_generic'),
 'Memcache': ('memcache_debug',),
 'Mhash': ('mhash_count',
           'mhash_get_block_size',
           'mhash_get_hash_name',
           'mhash_keygen_s2k',
           'mhash'),
 'Misc.': ('connection_aborted',
           'connection_status',
           'constant',
           'define',
           'defined',
           'die',
           'eval',
           'exit',
           'get_browser',
           '__halt_compiler',
           'highlight_file',
           'highlight_string',
           'hrtime',
           'ignore_user_abort',
           'pack',
           'php_strip_whitespace',
           'sapi_windows_cp_conv',
           'sapi_windows_cp_get',
           'sapi_windows_cp_is_utf8',
           'sapi_windows_cp_set',
           'sapi_windows_generate_ctrl_event',
           'sapi_windows_set_ctrl_handler',
           'sapi_windows_vt100_support',
           'show_source',
           'sleep',
           'sys_getloadavg',
           'time_nanosleep',
           'time_sleep_until',
           'uniqid',
           'unpack',
           'usleep'),
 'Multibyte String': ('mb_check_encoding',
                      'mb_chr',
                      'mb_convert_case',
                      'mb_convert_encoding',
                      'mb_convert_kana',
                      'mb_convert_variables',
                      'mb_decode_mimeheader',
                      'mb_decode_numericentity',
                      'mb_detect_encoding',
                      'mb_detect_order',
                      'mb_encode_mimeheader',
                      'mb_encode_numericentity',
                      'mb_encoding_aliases',
                      'mb_ereg_match',
                      'mb_ereg_replace_callback',
                      'mb_ereg_replace',
                      'mb_ereg_search_getpos',
                      'mb_ereg_search_getregs',
                      'mb_ereg_search_init',
                      'mb_ereg_search_pos',
                      'mb_ereg_search_regs',
                      'mb_ereg_search_setpos',
                      'mb_ereg_search',
                      'mb_ereg',
                      'mb_eregi_replace',
                      'mb_eregi',
                      'mb_get_info',
                      'mb_http_input',
                      'mb_http_output',
                      'mb_internal_encoding',
                      'mb_language',
                      'mb_list_encodings',
                      'mb_ord',
                      'mb_output_handler',
                      'mb_parse_str',
                      'mb_preferred_mime_name',
                      'mb_regex_encoding',
                      'mb_regex_set_options',
                      'mb_scrub',
                      'mb_send_mail',
                      'mb_split',
                      'mb_str_split',
                      'mb_strcut',
                      'mb_strimwidth',
                      'mb_stripos',
                      'mb_stristr',
                      'mb_strlen',
                      'mb_strpos',
                      'mb_strrchr',
                      'mb_strrichr',
                      'mb_strripos',
                      'mb_strrpos',
                      'mb_strstr',
                      'mb_strtolower',
                      'mb_strtoupper',
                      'mb_strwidth',
                      'mb_substitute_character',
                      'mb_substr_count',
                      'mb_substr'),
 'MySQL': ('mysql_affected_rows',
           'mysql_client_encoding',
           'mysql_close',
           'mysql_connect',
           'mysql_create_db',
           'mysql_data_seek',
           'mysql_db_name',
           'mysql_db_query',
           'mysql_drop_db',
           'mysql_errno',
           'mysql_error',
           'mysql_escape_string',
           'mysql_fetch_array',
           'mysql_fetch_assoc',
           'mysql_fetch_field',
           'mysql_fetch_lengths',
           'mysql_fetch_object',
           'mysql_fetch_row',
           'mysql_field_flags',
           'mysql_field_len',
           'mysql_field_name',
           'mysql_field_seek',
           'mysql_field_table',
           'mysql_field_type',
           'mysql_free_result',
           'mysql_get_client_info',
           'mysql_get_host_info',
           'mysql_get_proto_info',
           'mysql_get_server_info',
           'mysql_info',
           'mysql_insert_id',
           'mysql_list_dbs',
           'mysql_list_fields',
           'mysql_list_processes',
           'mysql_list_tables',
           'mysql_num_fields',
           'mysql_num_rows',
           'mysql_pconnect',
           'mysql_ping',
           'mysql_query',
           'mysql_real_escape_string',
           'mysql_result',
           'mysql_select_db',
           'mysql_set_charset',
           'mysql_stat',
           'mysql_tablename',
           'mysql_thread_id',
           'mysql_unbuffered_query'),
 'Mysql_xdevapi': ('expression', 'getSession'),
 'Network': ('checkdnsrr',
             'closelog',
             'dns_check_record',
             'dns_get_mx',
             'dns_get_record',
             'fsockopen',
             'gethostbyaddr',
             'gethostbyname',
             'gethostbynamel',
             'gethostname',
             'getmxrr',
             'getprotobyname',
             'getprotobynumber',
             'getservbyname',
             'getservbyport',
             'header_register_callback',
             'header_remove',
             'header',
             'headers_list',
             'headers_sent',
             'http_response_code',
             'inet_ntop',
             'inet_pton',
             'ip2long',
             'long2ip',
             'net_get_interfaces',
             'openlog',
             'pfsockopen',
             'setcookie',
             'setrawcookie',
             'socket_get_status',
             'socket_set_blocking',
             'socket_set_timeout',
             'syslog'),
 'OAuth': ('oauth_get_sbs', 'oauth_urlencode'),
 'OCI8': ('oci_bind_array_by_name',
          'oci_bind_by_name',
          'oci_cancel',
          'oci_client_version',
          'oci_close',
          'oci_commit',
          'oci_connect',
          'oci_define_by_name',
          'oci_error',
          'oci_execute',
          'oci_fetch_all',
          'oci_fetch_array',
          'oci_fetch_assoc',
          'oci_fetch_object',
          'oci_fetch_row',
          'oci_fetch',
          'oci_field_is_null',
          'oci_field_name',
          'oci_field_precision',
          'oci_field_scale',
          'oci_field_size',
          'oci_field_type_raw',
          'oci_field_type',
          'oci_free_descriptor',
          'oci_free_statement',
          'oci_get_implicit_resultset',
          'oci_lob_copy',
          'oci_lob_is_equal',
          'oci_new_collection',
          'oci_new_connect',
          'oci_new_cursor',
          'oci_new_descriptor',
          'oci_num_fields',
          'oci_num_rows',
          'oci_parse',
          'oci_password_change',
          'oci_pconnect',
          'oci_register_taf_callback',
          'oci_result',
          'oci_rollback',
          'oci_server_version',
          'oci_set_action',
          'oci_set_call_timeout',
          'oci_set_client_identifier',
          'oci_set_client_info',
          'oci_set_db_operation',
          'oci_set_edition',
          'oci_set_module_name',
          'oci_set_prefetch_lob',
          'oci_set_prefetch',
          'oci_statement_type',
          'oci_unregister_taf_callback'),
 'ODBC': ('odbc_autocommit',
          'odbc_binmode',
          'odbc_close_all',
          'odbc_close',
          'odbc_columnprivileges',
          'odbc_columns',
          'odbc_commit',
          'odbc_connect',
          'odbc_cursor',
          'odbc_data_source',
          'odbc_do',
          'odbc_error',
          'odbc_errormsg',
          'odbc_exec',
          'odbc_execute',
          'odbc_fetch_array',
          'odbc_fetch_into',
          'odbc_fetch_object',
          'odbc_fetch_row',
          'odbc_field_len',
          'odbc_field_name',
          'odbc_field_num',
          'odbc_field_precision',
          'odbc_field_scale',
          'odbc_field_type',
          'odbc_foreignkeys',
          'odbc_free_result',
          'odbc_gettypeinfo',
          'odbc_longreadlen',
          'odbc_next_result',
          'odbc_num_fields',
          'odbc_num_rows',
          'odbc_pconnect',
          'odbc_prepare',
          'odbc_primarykeys',
          'odbc_procedurecolumns',
          'odbc_procedures',
          'odbc_result_all',
          'odbc_result',
          'odbc_rollback',
          'odbc_setoption',
          'odbc_specialcolumns',
          'odbc_statistics',
          'odbc_tableprivileges',
          'odbc_tables'),
 'OPcache': ('opcache_compile_file',
             'opcache_get_configuration',
             'opcache_get_status',
             'opcache_invalidate',
             'opcache_is_script_cached',
             'opcache_reset'),
 'OpenAL': ('openal_buffer_create',
            'openal_buffer_data',
            'openal_buffer_destroy',
            'openal_buffer_get',
            'openal_buffer_loadwav',
            'openal_context_create',
            'openal_context_current',
            'openal_context_destroy',
            'openal_context_process',
            'openal_context_suspend',
            'openal_device_close',
            'openal_device_open',
            'openal_listener_get',
            'openal_listener_set',
            'openal_source_create',
            'openal_source_destroy',
            'openal_source_get',
            'openal_source_pause',
            'openal_source_play',
            'openal_source_rewind',
            'openal_source_set',
            'openal_source_stop',
            'openal_stream'),
 'OpenSSL': ('openssl_cipher_iv_length',
             'openssl_cms_decrypt',
             'openssl_cms_encrypt',
             'openssl_cms_read',
             'openssl_cms_sign',
             'openssl_cms_verify',
             'openssl_csr_export_to_file',
             'openssl_csr_export',
             'openssl_csr_get_public_key',
             'openssl_csr_get_subject',
             'openssl_csr_new',
             'openssl_csr_sign',
             'openssl_decrypt',
             'openssl_dh_compute_key',
             'openssl_digest',
             'openssl_encrypt',
             'openssl_error_string',
             'openssl_free_key',
             'openssl_get_cert_locations',
             'openssl_get_cipher_methods',
             'openssl_get_curve_names',
             'openssl_get_md_methods',
             'openssl_get_privatekey',
             'openssl_get_publickey',
             'openssl_open',
             'openssl_pbkdf2',
             'openssl_pkcs12_export_to_file',
             'openssl_pkcs12_export',
             'openssl_pkcs12_read',
             'openssl_pkcs7_decrypt',
             'openssl_pkcs7_encrypt',
             'openssl_pkcs7_read',
             'openssl_pkcs7_sign',
             'openssl_pkcs7_verify',
             'openssl_pkey_derive',
             'openssl_pkey_export_to_file',
             'openssl_pkey_export',
             'openssl_pkey_free',
             'openssl_pkey_get_details',
             'openssl_pkey_get_private',
             'openssl_pkey_get_public',
             'openssl_pkey_new',
             'openssl_private_decrypt',
             'openssl_private_encrypt',
             'openssl_public_decrypt',
             'openssl_public_encrypt',
             'openssl_random_pseudo_bytes',
             'openssl_seal',
             'openssl_sign',
             'openssl_spki_export_challenge',
             'openssl_spki_export',
             'openssl_spki_new',
             'openssl_spki_verify',
             'openssl_verify',
             'openssl_x509_check_private_key',
             'openssl_x509_checkpurpose',
             'openssl_x509_export_to_file',
             'openssl_x509_export',
             'openssl_x509_fingerprint',
             'openssl_x509_free',
             'openssl_x509_parse',
             'openssl_x509_read',
             'openssl_x509_verify'),
 'Output Control': ('flush',
                    'ob_clean',
                    'ob_end_clean',
                    'ob_end_flush',
                    'ob_flush',
                    'ob_get_clean',
                    'ob_get_contents',
                    'ob_get_flush',
                    'ob_get_length',
                    'ob_get_level',
                    'ob_get_status',
                    'ob_gzhandler',
                    'ob_implicit_flush',
                    'ob_list_handlers',
                    'ob_start',
                    'output_add_rewrite_var',
                    'output_reset_rewrite_vars'),
 'PCNTL': ('pcntl_alarm',
           'pcntl_async_signals',
           'pcntl_errno',
           'pcntl_exec',
           'pcntl_fork',
           'pcntl_get_last_error',
           'pcntl_getpriority',
           'pcntl_setpriority',
           'pcntl_signal_dispatch',
           'pcntl_signal_get_handler',
           'pcntl_signal',
           'pcntl_sigprocmask',
           'pcntl_sigtimedwait',
           'pcntl_sigwaitinfo',
           'pcntl_strerror',
           'pcntl_wait',
           'pcntl_waitpid',
           'pcntl_wexitstatus',
           'pcntl_wifexited',
           'pcntl_wifsignaled',
           'pcntl_wifstopped',
           'pcntl_wstopsig',
           'pcntl_wtermsig'),
 'PCRE': ('preg_filter',
          'preg_grep',
          'preg_last_error_msg',
          'preg_last_error',
          'preg_match_all',
          'preg_match',
          'preg_quote',
          'preg_replace_callback_array',
          'preg_replace_callback',
          'preg_replace',
          'preg_split'),
 'PHP Options/Info': ('assert_options',
                      'assert',
                      'cli_get_process_title',
                      'cli_set_process_title',
                      'dl',
                      'extension_loaded',
                      'gc_collect_cycles',
                      'gc_disable',
                      'gc_enable',
                      'gc_enabled',
                      'gc_mem_caches',
                      'gc_status',
                      'get_cfg_var',
                      'get_current_user',
                      'get_defined_constants',
                      'get_extension_funcs',
                      'get_include_path',
                      'get_included_files',
                      'get_loaded_extensions',
                      'get_magic_quotes_gpc',
                      'get_magic_quotes_runtime',
                      'get_required_files',
                      'get_resources',
                      'getenv',
                      'getlastmod',
                      'getmygid',
                      'getmyinode',
                      'getmypid',
                      'getmyuid',
                      'getopt',
                      'getrusage',
                      'ini_alter',
                      'ini_get_all',
                      'ini_get',
                      'ini_restore',
                      'ini_set',
                      'memory_get_peak_usage',
                      'memory_get_usage',
                      'php_ini_loaded_file',
                      'php_ini_scanned_files',
                      'php_sapi_name',
                      'php_uname',
                      'phpcredits',
                      'phpinfo',
                      'phpversion',
                      'putenv',
                      'restore_include_path',
                      'set_include_path',
                      'set_time_limit',
                      'sys_get_temp_dir',
                      'version_compare',
                      'zend_thread_id',
                      'zend_version'),
 'POSIX': ('posix_access',
           'posix_ctermid',
           'posix_errno',
           'posix_get_last_error',
           'posix_getcwd',
           'posix_getegid',
           'posix_geteuid',
           'posix_getgid',
           'posix_getgrgid',
           'posix_getgrnam',
           'posix_getgroups',
           'posix_getlogin',
           'posix_getpgid',
           'posix_getpgrp',
           'posix_getpid',
           'posix_getppid',
           'posix_getpwnam',
           'posix_getpwuid',
           'posix_getrlimit',
           'posix_getsid',
           'posix_getuid',
           'posix_initgroups',
           'posix_isatty',
           'posix_kill',
           'posix_mkfifo',
           'posix_mknod',
           'posix_setegid',
           'posix_seteuid',
           'posix_setgid',
           'posix_setpgid',
           'posix_setrlimit',
           'posix_setsid',
           'posix_setuid',
           'posix_strerror',
           'posix_times',
           'posix_ttyname',
           'posix_uname'),
 'PS': ('ps_add_bookmark',
        'ps_add_launchlink',
        'ps_add_locallink',
        'ps_add_note',
        'ps_add_pdflink',
        'ps_add_weblink',
        'ps_arc',
        'ps_arcn',
        'ps_begin_page',
        'ps_begin_pattern',
        'ps_begin_template',
        'ps_circle',
        'ps_clip',
        'ps_close_image',
        'ps_close',
        'ps_closepath_stroke',
        'ps_closepath',
        'ps_continue_text',
        'ps_curveto',
        'ps_delete',
        'ps_end_page',
        'ps_end_pattern',
        'ps_end_template',
        'ps_fill_stroke',
        'ps_fill',
        'ps_findfont',
        'ps_get_buffer',
        'ps_get_parameter',
        'ps_get_value',
        'ps_hyphenate',
        'ps_include_file',
        'ps_lineto',
        'ps_makespotcolor',
        'ps_moveto',
        'ps_new',
        'ps_open_file',
        'ps_open_image_file',
        'ps_open_image',
        'ps_open_memory_image',
        'ps_place_image',
        'ps_rect',
        'ps_restore',
        'ps_rotate',
        'ps_save',
        'ps_scale',
        'ps_set_border_color',
        'ps_set_border_dash',
        'ps_set_border_style',
        'ps_set_info',
        'ps_set_parameter',
        'ps_set_text_pos',
        'ps_set_value',
        'ps_setcolor',
        'ps_setdash',
        'ps_setflat',
        'ps_setfont',
        'ps_setgray',
        'ps_setlinecap',
        'ps_setlinejoin',
        'ps_setlinewidth',
        'ps_setmiterlimit',
        'ps_setoverprintmode',
        'ps_setpolydash',
        'ps_shading_pattern',
        'ps_shading',
        'ps_shfill',
        'ps_show_boxed',
        'ps_show_xy2',
        'ps_show_xy',
        'ps_show2',
        'ps_show',
        'ps_string_geometry',
        'ps_stringwidth',
        'ps_stroke',
        'ps_symbol_name',
        'ps_symbol_width',
        'ps_symbol',
        'ps_translate'),
 'Password Hashing': ('password_algos',
                      'password_get_info',
                      'password_hash',
                      'password_needs_rehash',
                      'password_verify'),
 'PostgreSQL': ('pg_affected_rows',
                'pg_cancel_query',
                'pg_client_encoding',
                'pg_close',
                'pg_connect_poll',
                'pg_connect',
                'pg_connection_busy',
                'pg_connection_reset',
                'pg_connection_status',
                'pg_consume_input',
                'pg_convert',
                'pg_copy_from',
                'pg_copy_to',
                'pg_dbname',
                'pg_delete',
                'pg_end_copy',
                'pg_escape_bytea',
                'pg_escape_identifier',
                'pg_escape_literal',
                'pg_escape_string',
                'pg_execute',
                'pg_fetch_all_columns',
                'pg_fetch_all',
                'pg_fetch_array',
                'pg_fetch_assoc',
                'pg_fetch_object',
                'pg_fetch_result',
                'pg_fetch_row',
                'pg_field_is_null',
                'pg_field_name',
                'pg_field_num',
                'pg_field_prtlen',
                'pg_field_size',
                'pg_field_table',
                'pg_field_type_oid',
                'pg_field_type',
                'pg_flush',
                'pg_free_result',
                'pg_get_notify',
                'pg_get_pid',
                'pg_get_result',
                'pg_host',
                'pg_insert',
                'pg_last_error',
                'pg_last_notice',
                'pg_last_oid',
                'pg_lo_close',
                'pg_lo_create',
                'pg_lo_export',
                'pg_lo_import',
                'pg_lo_open',
                'pg_lo_read_all',
                'pg_lo_read',
                'pg_lo_seek',
                'pg_lo_tell',
                'pg_lo_truncate',
                'pg_lo_unlink',
                'pg_lo_write',
                'pg_meta_data',
                'pg_num_fields',
                'pg_num_rows',
                'pg_options',
                'pg_parameter_status',
                'pg_pconnect',
                'pg_ping',
                'pg_port',
                'pg_prepare',
                'pg_put_line',
                'pg_query_params',
                'pg_query',
                'pg_result_error_field',
                'pg_result_error',
                'pg_result_seek',
                'pg_result_status',
                'pg_select',
                'pg_send_execute',
                'pg_send_prepare',
                'pg_send_query_params',
                'pg_send_query',
                'pg_set_client_encoding',
                'pg_set_error_verbosity',
                'pg_socket',
                'pg_trace',
                'pg_transaction_status',
                'pg_tty',
                'pg_unescape_bytea',
                'pg_untrace',
                'pg_update',
                'pg_version'),
 'Program execution': ('escapeshellarg',
                       'escapeshellcmd',
                       'exec',
                       'passthru',
                       'proc_close',
                       'proc_get_status',
                       'proc_nice',
                       'proc_open',
                       'proc_terminate',
                       'shell_exec',
                       'system'),
 'Pspell': ('pspell_add_to_personal',
            'pspell_add_to_session',
            'pspell_check',
            'pspell_clear_session',
            'pspell_config_create',
            'pspell_config_data_dir',
            'pspell_config_dict_dir',
            'pspell_config_ignore',
            'pspell_config_mode',
            'pspell_config_personal',
            'pspell_config_repl',
            'pspell_config_runtogether',
            'pspell_config_save_repl',
            'pspell_new_config',
            'pspell_new_personal',
            'pspell_new',
            'pspell_save_wordlist',
            'pspell_store_replacement',
            'pspell_suggest'),
 'RRD': ('rrd_create',
         'rrd_error',
         'rrd_fetch',
         'rrd_first',
         'rrd_graph',
         'rrd_info',
         'rrd_last',
         'rrd_lastupdate',
         'rrd_restore',
         'rrd_tune',
         'rrd_update',
         'rrd_version',
         'rrd_xport',
         'rrdc_disconnect'),
 'Radius': ('radius_acct_open',
            'radius_add_server',
            'radius_auth_open',
            'radius_close',
            'radius_config',
            'radius_create_request',
            'radius_cvt_addr',
            'radius_cvt_int',
            'radius_cvt_string',
            'radius_demangle_mppe_key',
            'radius_demangle',
            'radius_get_attr',
            'radius_get_tagged_attr_data',
            'radius_get_tagged_attr_tag',
            'radius_get_vendor_attr',
            'radius_put_addr',
            'radius_put_attr',
            'radius_put_int',
            'radius_put_string',
            'radius_put_vendor_addr',
            'radius_put_vendor_attr',
            'radius_put_vendor_int',
            'radius_put_vendor_string',
            'radius_request_authenticator',
            'radius_salt_encrypt_attr',
            'radius_send_request',
            'radius_server_secret',
            'radius_strerror'),
 'Rar': ('rar_wrapper_cache_stats',),
 'Readline': ('readline_add_history',
              'readline_callback_handler_install',
              'readline_callback_handler_remove',
              'readline_callback_read_char',
              'readline_clear_history',
              'readline_completion_function',
              'readline_info',
              'readline_list_history',
              'readline_on_new_line',
              'readline_read_history',
              'readline_redisplay',
              'readline_write_history',
              'readline'),
 'Recode': ('recode_file', 'recode_string', 'recode'),
 'RpmInfo': ('rpmaddtag', 'rpmdbinfo', 'rpmdbsearch', 'rpminfo', 'rpmvercmp'),
 'SNMP': ('snmp_get_quick_print',
          'snmp_get_valueretrieval',
          'snmp_read_mib',
          'snmp_set_enum_print',
          'snmp_set_oid_numeric_print',
          'snmp_set_oid_output_format',
          'snmp_set_quick_print',
          'snmp_set_valueretrieval',
          'snmp2_get',
          'snmp2_getnext',
          'snmp2_real_walk',
          'snmp2_set',
          'snmp2_walk',
          'snmp3_get',
          'snmp3_getnext',
          'snmp3_real_walk',
          'snmp3_set',
          'snmp3_walk',
          'snmpget',
          'snmpgetnext',
          'snmprealwalk',
          'snmpset',
          'snmpwalk',
          'snmpwalkoid'),
 'SOAP': ('is_soap_fault', 'use_soap_error_handler'),
 'SPL': ('class_implements',
         'class_parents',
         'class_uses',
         'iterator_apply',
         'iterator_count',
         'iterator_to_array',
         'spl_autoload_call',
         'spl_autoload_extensions',
         'spl_autoload_functions',
         'spl_autoload_register',
         'spl_autoload_unregister',
         'spl_autoload',
         'spl_classes',
         'spl_object_hash',
         'spl_object_id'),
 'SQLSRV': ('sqlsrv_begin_transaction',
            'sqlsrv_cancel',
            'sqlsrv_client_info',
            'sqlsrv_close',
            'sqlsrv_commit',
            'sqlsrv_configure',
            'sqlsrv_connect',
            'sqlsrv_errors',
            'sqlsrv_execute',
            'sqlsrv_fetch_array',
            'sqlsrv_fetch_object',
            'sqlsrv_fetch',
            'sqlsrv_field_metadata',
            'sqlsrv_free_stmt',
            'sqlsrv_get_config',
            'sqlsrv_get_field',
            'sqlsrv_has_rows',
            'sqlsrv_next_result',
            'sqlsrv_num_fields',
            'sqlsrv_num_rows',
            'sqlsrv_prepare',
            'sqlsrv_query',
            'sqlsrv_rollback',
            'sqlsrv_rows_affected',
            'sqlsrv_send_stream_data',
            'sqlsrv_server_info'),
 'SSH2': ('ssh2_auth_agent',
          'ssh2_auth_hostbased_file',
          'ssh2_auth_none',
          'ssh2_auth_password',
          'ssh2_auth_pubkey_file',
          'ssh2_connect',
          'ssh2_disconnect',
          'ssh2_exec',
          'ssh2_fetch_stream',
          'ssh2_fingerprint',
          'ssh2_forward_accept',
          'ssh2_forward_listen',
          'ssh2_methods_negotiated',
          'ssh2_poll',
          'ssh2_publickey_add',
          'ssh2_publickey_init',
          'ssh2_publickey_list',
          'ssh2_publickey_remove',
          'ssh2_scp_recv',
          'ssh2_scp_send',
          'ssh2_send_eof',
          'ssh2_sftp_chmod',
          'ssh2_sftp_lstat',
          'ssh2_sftp_mkdir',
          'ssh2_sftp_readlink',
          'ssh2_sftp_realpath',
          'ssh2_sftp_rename',
          'ssh2_sftp_rmdir',
          'ssh2_sftp_stat',
          'ssh2_sftp_symlink',
          'ssh2_sftp_unlink',
          'ssh2_sftp',
          'ssh2_shell',
          'ssh2_tunnel'),
 'SVN': ('svn_add',
         'svn_auth_get_parameter',
         'svn_auth_set_parameter',
         'svn_blame',
         'svn_cat',
         'svn_checkout',
         'svn_cleanup',
         'svn_client_version',
         'svn_commit',
         'svn_delete',
         'svn_diff',
         'svn_export',
         'svn_fs_abort_txn',
         'svn_fs_apply_text',
         'svn_fs_begin_txn2',
         'svn_fs_change_node_prop',
         'svn_fs_check_path',
         'svn_fs_contents_changed',
         'svn_fs_copy',
         'svn_fs_delete',
         'svn_fs_dir_entries',
         'svn_fs_file_contents',
         'svn_fs_file_length',
         'svn_fs_is_dir',
         'svn_fs_is_file',
         'svn_fs_make_dir',
         'svn_fs_make_file',
         'svn_fs_node_created_rev',
         'svn_fs_node_prop',
         'svn_fs_props_changed',
         'svn_fs_revision_prop',
         'svn_fs_revision_root',
         'svn_fs_txn_root',
         'svn_fs_youngest_rev',
         'svn_import',
         'svn_log',
         'svn_ls',
         'svn_mkdir',
         'svn_repos_create',
         'svn_repos_fs_begin_txn_for_commit',
         'svn_repos_fs_commit_txn',
         'svn_repos_fs',
         'svn_repos_hotcopy',
         'svn_repos_open',
         'svn_repos_recover',
         'svn_revert',
         'svn_status',
         'svn_update'),
 'Scoutapm': ('scoutapm_get_calls', 'scoutapm_list_instrumented_functions'),
 'Seaslog': ('seaslog_get_author', 'seaslog_get_version'),
 'Semaphore': ('ftok',
               'msg_get_queue',
               'msg_queue_exists',
               'msg_receive',
               'msg_remove_queue',
               'msg_send',
               'msg_set_queue',
               'msg_stat_queue',
               'sem_acquire',
               'sem_get',
               'sem_release',
               'sem_remove',
               'shm_attach',
               'shm_detach',
               'shm_get_var',
               'shm_has_var',
               'shm_put_var',
               'shm_remove_var',
               'shm_remove'),
 'Session': ('session_abort',
             'session_cache_expire',
             'session_cache_limiter',
             'session_commit',
             'session_create_id',
             'session_decode',
             'session_destroy',
             'session_encode',
             'session_gc',
             'session_get_cookie_params',
             'session_id',
             'session_module_name',
             'session_name',
             'session_regenerate_id',
             'session_register_shutdown',
             'session_reset',
             'session_save_path',
             'session_set_cookie_params',
             'session_set_save_handler',
             'session_start',
             'session_status',
             'session_unset',
             'session_write_close'),
 'Shared Memory': ('shmop_close',
                   'shmop_delete',
                   'shmop_open',
                   'shmop_read',
                   'shmop_size',
                   'shmop_write'),
 'SimpleXML': ('simplexml_import_dom',
               'simplexml_load_file',
               'simplexml_load_string'),
 'Socket': ('socket_accept',
            'socket_addrinfo_bind',
            'socket_addrinfo_connect',
            'socket_addrinfo_explain',
            'socket_addrinfo_lookup',
            'socket_bind',
            'socket_clear_error',
            'socket_close',
            'socket_cmsg_space',
            'socket_connect',
            'socket_create_listen',
            'socket_create_pair',
            'socket_create',
            'socket_export_stream',
            'socket_get_option',
            'socket_getopt',
            'socket_getpeername',
            'socket_getsockname',
            'socket_import_stream',
            'socket_last_error',
            'socket_listen',
            'socket_read',
            'socket_recv',
            'socket_recvfrom',
            'socket_recvmsg',
            'socket_select',
            'socket_send',
            'socket_sendmsg',
            'socket_sendto',
            'socket_set_block',
            'socket_set_nonblock',
            'socket_set_option',
            'socket_setopt',
            'socket_shutdown',
            'socket_strerror',
            'socket_write',
            'socket_wsaprotocol_info_export',
            'socket_wsaprotocol_info_import',
            'socket_wsaprotocol_info_release'),
 'Sodium': ('sodium_add',
            'sodium_base642bin',
            'sodium_bin2base64',
            'sodium_bin2hex',
            'sodium_compare',
            'sodium_crypto_aead_aes256gcm_decrypt',
            'sodium_crypto_aead_aes256gcm_encrypt',
            'sodium_crypto_aead_aes256gcm_is_available',
            'sodium_crypto_aead_aes256gcm_keygen',
            'sodium_crypto_aead_chacha20poly1305_decrypt',
            'sodium_crypto_aead_chacha20poly1305_encrypt',
            'sodium_crypto_aead_chacha20poly1305_ietf_decrypt',
            'sodium_crypto_aead_chacha20poly1305_ietf_encrypt',
            'sodium_crypto_aead_chacha20poly1305_ietf_keygen',
            'sodium_crypto_aead_chacha20poly1305_keygen',
            'sodium_crypto_aead_xchacha20poly1305_ietf_decrypt',
            'sodium_crypto_aead_xchacha20poly1305_ietf_encrypt',
            'sodium_crypto_aead_xchacha20poly1305_ietf_keygen',
            'sodium_crypto_auth_keygen',
            'sodium_crypto_auth_verify',
            'sodium_crypto_auth',
            'sodium_crypto_box_keypair_from_secretkey_and_publickey',
            'sodium_crypto_box_keypair',
            'sodium_crypto_box_open',
            'sodium_crypto_box_publickey_from_secretkey',
            'sodium_crypto_box_publickey',
            'sodium_crypto_box_seal_open',
            'sodium_crypto_box_seal',
            'sodium_crypto_box_secretkey',
            'sodium_crypto_box_seed_keypair',
            'sodium_crypto_box',
            'sodium_crypto_generichash_final',
            'sodium_crypto_generichash_init',
            'sodium_crypto_generichash_keygen',
            'sodium_crypto_generichash_update',
            'sodium_crypto_generichash',
            'sodium_crypto_kdf_derive_from_key',
            'sodium_crypto_kdf_keygen',
            'sodium_crypto_kx_client_session_keys',
            'sodium_crypto_kx_keypair',
            'sodium_crypto_kx_publickey',
            'sodium_crypto_kx_secretkey',
            'sodium_crypto_kx_seed_keypair',
            'sodium_crypto_kx_server_session_keys',
            'sodium_crypto_pwhash_scryptsalsa208sha256_str_verify',
            'sodium_crypto_pwhash_scryptsalsa208sha256_str',
            'sodium_crypto_pwhash_scryptsalsa208sha256',
            'sodium_crypto_pwhash_str_needs_rehash',
            'sodium_crypto_pwhash_str_verify',
            'sodium_crypto_pwhash_str',
            'sodium_crypto_pwhash',
            'sodium_crypto_scalarmult_base',
            'sodium_crypto_scalarmult',
            'sodium_crypto_secretbox_keygen',
            'sodium_crypto_secretbox_open',
            'sodium_crypto_secretbox',
            'sodium_crypto_secretstream_xchacha20poly1305_init_pull',
            'sodium_crypto_secretstream_xchacha20poly1305_init_push',
            'sodium_crypto_secretstream_xchacha20poly1305_keygen',
            'sodium_crypto_secretstream_xchacha20poly1305_pull',
            'sodium_crypto_secretstream_xchacha20poly1305_push',
            'sodium_crypto_secretstream_xchacha20poly1305_rekey',
            'sodium_crypto_shorthash_keygen',
            'sodium_crypto_shorthash',
            'sodium_crypto_sign_detached',
            'sodium_crypto_sign_ed25519_pk_to_curve25519',
            'sodium_crypto_sign_ed25519_sk_to_curve25519',
            'sodium_crypto_sign_keypair_from_secretkey_and_publickey',
            'sodium_crypto_sign_keypair',
            'sodium_crypto_sign_open',
            'sodium_crypto_sign_publickey_from_secretkey',
            'sodium_crypto_sign_publickey',
            'sodium_crypto_sign_secretkey',
            'sodium_crypto_sign_seed_keypair',
            'sodium_crypto_sign_verify_detached',
            'sodium_crypto_sign',
            'sodium_crypto_stream_keygen',
            'sodium_crypto_stream_xor',
            'sodium_crypto_stream',
            'sodium_hex2bin',
            'sodium_increment',
            'sodium_memcmp',
            'sodium_memzero',
            'sodium_pad',
            'sodium_unpad'),
 'Solr': ('solr_get_version',),
 'Stomp': ('stomp_connect_error', 'stomp_version'),
 'Stream': ('stream_bucket_append',
            'stream_bucket_make_writeable',
            'stream_bucket_new',
            'stream_bucket_prepend',
            'stream_context_create',
            'stream_context_get_default',
            'stream_context_get_options',
            'stream_context_get_params',
            'stream_context_set_default',
            'stream_context_set_option',
            'stream_context_set_params',
            'stream_copy_to_stream',
            'stream_filter_append',
            'stream_filter_prepend',
            'stream_filter_register',
            'stream_filter_remove',
            'stream_get_contents',
            'stream_get_filters',
            'stream_get_line',
            'stream_get_meta_data',
            'stream_get_transports',
            'stream_get_wrappers',
            'stream_is_local',
            'stream_isatty',
            'stream_notification_callback',
            'stream_register_wrapper',
            'stream_resolve_include_path',
            'stream_select',
            'stream_set_blocking',
            'stream_set_chunk_size',
            'stream_set_read_buffer',
            'stream_set_timeout',
            'stream_set_write_buffer',
            'stream_socket_accept',
            'stream_socket_client',
            'stream_socket_enable_crypto',
            'stream_socket_get_name',
            'stream_socket_pair',
            'stream_socket_recvfrom',
            'stream_socket_sendto',
            'stream_socket_server',
            'stream_socket_shutdown',
            'stream_supports_lock',
            'stream_wrapper_register',
            'stream_wrapper_restore',
            'stream_wrapper_unregister'),
 'String': ('addcslashes',
            'addslashes',
            'bin2hex',
            'chop',
            'chr',
            'chunk_split',
            'convert_cyr_string',
            'convert_uudecode',
            'convert_uuencode',
            'count_chars',
            'crc32',
            'crypt',
            'echo',
            'explode',
            'fprintf',
            'get_html_translation_table',
            'hebrev',
            'hebrevc',
            'hex2bin',
            'html_entity_decode',
            'htmlentities',
            'htmlspecialchars_decode',
            'htmlspecialchars',
            'implode',
            'join',
            'lcfirst',
            'levenshtein',
            'localeconv',
            'ltrim',
            'md5_file',
            'md5',
            'metaphone',
            'money_format',
            'nl_langinfo',
            'nl2br',
            'number_format',
            'ord',
            'parse_str',
            'print',
            'printf',
            'quoted_printable_decode',
            'quoted_printable_encode',
            'quotemeta',
            'rtrim',
            'setlocale',
            'sha1_file',
            'sha1',
            'similar_text',
            'soundex',
            'sprintf',
            'sscanf',
            'str_contains',
            'str_ends_with',
            'str_getcsv',
            'str_ireplace',
            'str_pad',
            'str_repeat',
            'str_replace',
            'str_rot13',
            'str_shuffle',
            'str_split',
            'str_starts_with',
            'str_word_count',
            'strcasecmp',
            'strchr',
            'strcmp',
            'strcoll',
            'strcspn',
            'strip_tags',
            'stripcslashes',
            'stripos',
            'stripslashes',
            'stristr',
            'strlen',
            'strnatcasecmp',
            'strnatcmp',
            'strncasecmp',
            'strncmp',
            'strpbrk',
            'strpos',
            'strrchr',
            'strrev',
            'strripos',
            'strrpos',
            'strspn',
            'strstr',
            'strtok',
            'strtolower',
            'strtoupper',
            'strtr',
            'substr_compare',
            'substr_count',
            'substr_replace',
            'substr',
            'trim',
            'ucfirst',
            'ucwords',
            'vfprintf',
            'vprintf',
            'vsprintf',
            'wordwrap'),
 'Swoole': ('swoole_async_dns_lookup',
            'swoole_async_read',
            'swoole_async_readfile',
            'swoole_async_set',
            'swoole_async_write',
            'swoole_async_writefile',
            'swoole_clear_error',
            'swoole_client_select',
            'swoole_cpu_num',
            'swoole_errno',
            'swoole_error_log',
            'swoole_event_add',
            'swoole_event_defer',
            'swoole_event_del',
            'swoole_event_exit',
            'swoole_event_set',
            'swoole_event_wait',
            'swoole_event_write',
            'swoole_get_local_ip',
            'swoole_last_error',
            'swoole_load_module',
            'swoole_select',
            'swoole_set_process_name',
            'swoole_strerror',
            'swoole_timer_after',
            'swoole_timer_exists',
            'swoole_timer_tick',
            'swoole_version'),
 'TCP': ('tcpwrap_check',),
 'Taint': ('is_tainted', 'taint', 'untaint'),
 'Tidy': ('ob_tidyhandler',
          'tidy_access_count',
          'tidy_config_count',
          'tidy_error_count',
          'tidy_get_output',
          'tidy_warning_count'),
 'Tokenizer': ('token_get_all', 'token_name'),
 'Trader': ('trader_acos',
            'trader_ad',
            'trader_add',
            'trader_adosc',
            'trader_adx',
            'trader_adxr',
            'trader_apo',
            'trader_aroon',
            'trader_aroonosc',
            'trader_asin',
            'trader_atan',
            'trader_atr',
            'trader_avgprice',
            'trader_bbands',
            'trader_beta',
            'trader_bop',
            'trader_cci',
            'trader_cdl2crows',
            'trader_cdl3blackcrows',
            'trader_cdl3inside',
            'trader_cdl3linestrike',
            'trader_cdl3outside',
            'trader_cdl3starsinsouth',
            'trader_cdl3whitesoldiers',
            'trader_cdlabandonedbaby',
            'trader_cdladvanceblock',
            'trader_cdlbelthold',
            'trader_cdlbreakaway',
            'trader_cdlclosingmarubozu',
            'trader_cdlconcealbabyswall',
            'trader_cdlcounterattack',
            'trader_cdldarkcloudcover',
            'trader_cdldoji',
            'trader_cdldojistar',
            'trader_cdldragonflydoji',
            'trader_cdlengulfing',
            'trader_cdleveningdojistar',
            'trader_cdleveningstar',
            'trader_cdlgapsidesidewhite',
            'trader_cdlgravestonedoji',
            'trader_cdlhammer',
            'trader_cdlhangingman',
            'trader_cdlharami',
            'trader_cdlharamicross',
            'trader_cdlhighwave',
            'trader_cdlhikkake',
            'trader_cdlhikkakemod',
            'trader_cdlhomingpigeon',
            'trader_cdlidentical3crows',
            'trader_cdlinneck',
            'trader_cdlinvertedhammer',
            'trader_cdlkicking',
            'trader_cdlkickingbylength',
            'trader_cdlladderbottom',
            'trader_cdllongleggeddoji',
            'trader_cdllongline',
            'trader_cdlmarubozu',
            'trader_cdlmatchinglow',
            'trader_cdlmathold',
            'trader_cdlmorningdojistar',
            'trader_cdlmorningstar',
            'trader_cdlonneck',
            'trader_cdlpiercing',
            'trader_cdlrickshawman',
            'trader_cdlrisefall3methods',
            'trader_cdlseparatinglines',
            'trader_cdlshootingstar',
            'trader_cdlshortline',
            'trader_cdlspinningtop',
            'trader_cdlstalledpattern',
            'trader_cdlsticksandwich',
            'trader_cdltakuri',
            'trader_cdltasukigap',
            'trader_cdlthrusting',
            'trader_cdltristar',
            'trader_cdlunique3river',
            'trader_cdlupsidegap2crows',
            'trader_cdlxsidegap3methods',
            'trader_ceil',
            'trader_cmo',
            'trader_correl',
            'trader_cos',
            'trader_cosh',
            'trader_dema',
            'trader_div',
            'trader_dx',
            'trader_ema',
            'trader_errno',
            'trader_exp',
            'trader_floor',
            'trader_get_compat',
            'trader_get_unstable_period',
            'trader_ht_dcperiod',
            'trader_ht_dcphase',
            'trader_ht_phasor',
            'trader_ht_sine',
            'trader_ht_trendline',
            'trader_ht_trendmode',
            'trader_kama',
            'trader_linearreg_angle',
            'trader_linearreg_intercept',
            'trader_linearreg_slope',
            'trader_linearreg',
            'trader_ln',
            'trader_log10',
            'trader_ma',
            'trader_macd',
            'trader_macdext',
            'trader_macdfix',
            'trader_mama',
            'trader_mavp',
            'trader_max',
            'trader_maxindex',
            'trader_medprice',
            'trader_mfi',
            'trader_midpoint',
            'trader_midprice',
            'trader_min',
            'trader_minindex',
            'trader_minmax',
            'trader_minmaxindex',
            'trader_minus_di',
            'trader_minus_dm',
            'trader_mom',
            'trader_mult',
            'trader_natr',
            'trader_obv',
            'trader_plus_di',
            'trader_plus_dm',
            'trader_ppo',
            'trader_roc',
            'trader_rocp',
            'trader_rocr100',
            'trader_rocr',
            'trader_rsi',
            'trader_sar',
            'trader_sarext',
            'trader_set_compat',
            'trader_set_unstable_period',
            'trader_sin',
            'trader_sinh',
            'trader_sma',
            'trader_sqrt',
            'trader_stddev',
            'trader_stoch',
            'trader_stochf',
            'trader_stochrsi',
            'trader_sub',
            'trader_sum',
            'trader_t3',
            'trader_tan',
            'trader_tanh',
            'trader_tema',
            'trader_trange',
            'trader_trima',
            'trader_trix',
            'trader_tsf',
            'trader_typprice',
            'trader_ultosc',
            'trader_var',
            'trader_wclprice',
            'trader_willr',
            'trader_wma'),
 'URL': ('base64_decode',
         'base64_encode',
         'get_headers',
         'get_meta_tags',
         'http_build_query',
         'parse_url',
         'rawurldecode',
         'rawurlencode',
         'urldecode',
         'urlencode'),
 'Uopz': ('uopz_add_function',
          'uopz_allow_exit',
          'uopz_backup',
          'uopz_compose',
          'uopz_copy',
          'uopz_del_function',
          'uopz_delete',
          'uopz_extend',
          'uopz_flags',
          'uopz_function',
          'uopz_get_exit_status',
          'uopz_get_hook',
          'uopz_get_mock',
          'uopz_get_property',
          'uopz_get_return',
          'uopz_get_static',
          'uopz_implement',
          'uopz_overload',
          'uopz_redefine',
          'uopz_rename',
          'uopz_restore',
          'uopz_set_hook',
          'uopz_set_mock',
          'uopz_set_property',
          'uopz_set_return',
          'uopz_set_static',
          'uopz_undefine',
          'uopz_unset_hook',
          'uopz_unset_mock',
          'uopz_unset_return'),
 'Variable handling': ('boolval',
                       'debug_zval_dump',
                       'doubleval',
                       'empty',
                       'floatval',
                       'get_debug_type',
                       'get_defined_vars',
                       'get_resource_id',
                       'get_resource_type',
                       'gettype',
                       'intval',
                       'is_array',
                       'is_bool',
                       'is_callable',
                       'is_countable',
                       'is_double',
                       'is_float',
                       'is_int',
                       'is_integer',
                       'is_iterable',
                       'is_long',
                       'is_null',
                       'is_numeric',
                       'is_object',
                       'is_real',
                       'is_resource',
                       'is_scalar',
                       'is_string',
                       'isset',
                       'print_r',
                       'serialize',
                       'settype',
                       'strval',
                       'unserialize',
                       'unset',
                       'var_dump',
                       'var_export'),
 'WDDX': ('wddx_add_vars',
          'wddx_deserialize',
          'wddx_packet_end',
          'wddx_packet_start',
          'wddx_serialize_value',
          'wddx_serialize_vars'),
 'WinCache': ('wincache_fcache_fileinfo',
              'wincache_fcache_meminfo',
              'wincache_lock',
              'wincache_ocache_fileinfo',
              'wincache_ocache_meminfo',
              'wincache_refresh_if_changed',
              'wincache_rplist_fileinfo',
              'wincache_rplist_meminfo',
              'wincache_scache_info',
              'wincache_scache_meminfo',
              'wincache_ucache_add',
              'wincache_ucache_cas',
              'wincache_ucache_clear',
              'wincache_ucache_dec',
              'wincache_ucache_delete',
              'wincache_ucache_exists',
              'wincache_ucache_get',
              'wincache_ucache_inc',
              'wincache_ucache_info',
              'wincache_ucache_meminfo',
              'wincache_ucache_set',
              'wincache_unlock'),
 'XML Parser': ('utf8_decode',
                'utf8_encode',
                'xml_error_string',
                'xml_get_current_byte_index',
                'xml_get_current_column_number',
                'xml_get_current_line_number',
                'xml_get_error_code',
                'xml_parse_into_struct',
                'xml_parse',
                'xml_parser_create_ns',
                'xml_parser_create',
                'xml_parser_free',
                'xml_parser_get_option',
                'xml_parser_set_option',
                'xml_set_character_data_handler',
                'xml_set_default_handler',
                'xml_set_element_handler',
                'xml_set_end_namespace_decl_handler',
                'xml_set_external_entity_ref_handler',
                'xml_set_notation_decl_handler',
                'xml_set_object',
                'xml_set_processing_instruction_handler',
                'xml_set_start_namespace_decl_handler',
                'xml_set_unparsed_entity_decl_handler'),
 'XML-RPC': ('xmlrpc_decode_request',
             'xmlrpc_decode',
             'xmlrpc_encode_request',
             'xmlrpc_encode',
             'xmlrpc_get_type',
             'xmlrpc_is_fault',
             'xmlrpc_parse_method_descriptions',
             'xmlrpc_server_add_introspection_data',
             'xmlrpc_server_call_method',
             'xmlrpc_server_create',
             'xmlrpc_server_destroy',
             'xmlrpc_server_register_introspection_callback',
             'xmlrpc_server_register_method',
             'xmlrpc_set_type'),
 'Xhprof': ('xhprof_disable',
            'xhprof_enable',
            'xhprof_sample_disable',
            'xhprof_sample_enable'),
 'YAZ': ('yaz_addinfo',
         'yaz_ccl_conf',
         'yaz_ccl_parse',
         'yaz_close',
         'yaz_connect',
         'yaz_database',
         'yaz_element',
         'yaz_errno',
         'yaz_error',
         'yaz_es_result',
         'yaz_es',
         'yaz_get_option',
         'yaz_hits',
         'yaz_itemorder',
         'yaz_present',
         'yaz_range',
         'yaz_record',
         'yaz_scan_result',
         'yaz_scan',
         'yaz_schema',
         'yaz_search',
         'yaz_set_option',
         'yaz_sort',
         'yaz_syntax',
         'yaz_wait'),
 'Yaml': ('yaml_emit_file',
          'yaml_emit',
          'yaml_parse_file',
          'yaml_parse_url',
          'yaml_parse'),
 'Zip': ('zip_close',
         'zip_entry_close',
         'zip_entry_compressedsize',
         'zip_entry_compressionmethod',
         'zip_entry_filesize',
         'zip_entry_name',
         'zip_entry_open',
         'zip_entry_read',
         'zip_open',
         'zip_read'),
 'Zlib': ('deflate_add',
          'deflate_init',
          'gzclose',
          'gzcompress',
          'gzdecode',
          'gzdeflate',
          'gzencode',
          'gzeof',
          'gzfile',
          'gzgetc',
          'gzgets',
          'gzgetss',
          'gzinflate',
          'gzopen',
          'gzpassthru',
          'gzputs',
          'gzread',
          'gzrewind',
          'gzseek',
          'gztell',
          'gzuncompress',
          'gzwrite',
          'inflate_add',
          'inflate_get_read_len',
          'inflate_get_status',
          'inflate_init',
          'readgzfile',
          'zlib_decode',
          'zlib_encode',
          'zlib_get_coding_type'),
 'ZooKeeper': ('zookeeper_dispatch',),
 'cURL': ('curl_close',
          'curl_copy_handle',
          'curl_errno',
          'curl_error',
          'curl_escape',
          'curl_exec',
          'curl_file_create',
          'curl_getinfo',
          'curl_init',
          'curl_multi_add_handle',
          'curl_multi_close',
          'curl_multi_errno',
          'curl_multi_exec',
          'curl_multi_getcontent',
          'curl_multi_info_read',
          'curl_multi_init',
          'curl_multi_remove_handle',
          'curl_multi_select',
          'curl_multi_setopt',
          'curl_multi_strerror',
          'curl_pause',
          'curl_reset',
          'curl_setopt_array',
          'curl_setopt',
          'curl_share_close',
          'curl_share_errno',
          'curl_share_init',
          'curl_share_setopt',
          'curl_share_strerror',
          'curl_strerror',
          'curl_unescape',
          'curl_version'),
 'dBase': ('dbase_add_record',
           'dbase_close',
           'dbase_create',
           'dbase_delete_record',
           'dbase_get_header_info',
           'dbase_get_record_with_names',
           'dbase_get_record',
           'dbase_numfields',
           'dbase_numrecords',
           'dbase_open',
           'dbase_pack',
           'dbase_replace_record'),
 'iconv': ('iconv_get_encoding',
           'iconv_mime_decode_headers',
           'iconv_mime_decode',
           'iconv_mime_encode',
           'iconv_set_encoding',
           'iconv_strlen',
           'iconv_strpos',
           'iconv_strrpos',
           'iconv_substr',
           'iconv',
           'ob_iconv_handler'),
 'intl': ('intl_error_name',
          'intl_get_error_code',
          'intl_get_error_message',
          'intl_is_failure'),
 'libxml': ('libxml_clear_errors',
            'libxml_disable_entity_loader',
            'libxml_get_errors',
            'libxml_get_last_error',
            'libxml_set_external_entity_loader',
            'libxml_set_streams_context',
            'libxml_use_internal_errors'),
 'mqseries': ('mqseries_back',
              'mqseries_begin',
              'mqseries_close',
              'mqseries_cmit',
              'mqseries_conn',
              'mqseries_connx',
              'mqseries_disc',
              'mqseries_get',
              'mqseries_inq',
              'mqseries_open',
              'mqseries_put1',
              'mqseries_put',
              'mqseries_set',
              'mqseries_strerror'),
 'phpdbg': ('phpdbg_break_file',
            'phpdbg_break_function',
            'phpdbg_break_method',
            'phpdbg_break_next',
            'phpdbg_clear',
            'phpdbg_color',
            'phpdbg_end_oplog',
            'phpdbg_exec',
            'phpdbg_get_executable',
            'phpdbg_prompt',
            'phpdbg_start_oplog'),
 'runkit7': ('runkit7_constant_add',
             'runkit7_constant_redefine',
             'runkit7_constant_remove',
             'runkit7_function_add',
             'runkit7_function_copy',
             'runkit7_function_redefine',
             'runkit7_function_remove',
             'runkit7_function_rename',
             'runkit7_import',
             'runkit7_method_add',
             'runkit7_method_copy',
             'runkit7_method_redefine',
             'runkit7_method_remove',
             'runkit7_method_rename',
             'runkit7_object_id',
             'runkit7_superglobals',
             'runkit7_zval_inspect'),
 'ssdeep': ('ssdeep_fuzzy_compare',
            'ssdeep_fuzzy_hash_filename',
            'ssdeep_fuzzy_hash'),
 'var_representation': ('var_representation',),
 'win32service': ('win32_continue_service',
                  'win32_create_service',
                  'win32_delete_service',
                  'win32_get_last_control_message',
                  'win32_pause_service',
                  'win32_query_service_status',
                  'win32_send_custom_control',
                  'win32_set_service_exit_code',
                  'win32_set_service_exit_mode',
                  'win32_set_service_status',
                  'win32_start_service_ctrl_dispatcher',
                  'win32_start_service',
                  'win32_stop_service'),
 'xattr': ('xattr_get',
           'xattr_list',
           'xattr_remove',
           'xattr_set',
           'xattr_supported'),
 'xdiff': ('xdiff_file_bdiff_size',
           'xdiff_file_bdiff',
           'xdiff_file_bpatch',
           'xdiff_file_diff_binary',
           'xdiff_file_diff',
           'xdiff_file_merge3',
           'xdiff_file_patch_binary',
           'xdiff_file_patch',
           'xdiff_file_rabdiff',
           'xdiff_string_bdiff_size',
           'xdiff_string_bdiff',
           'xdiff_string_bpatch',
           'xdiff_string_diff_binary',
           'xdiff_string_diff',
           'xdiff_string_merge3',
           'xdiff_string_patch_binary',
           'xdiff_string_patch',
           'xdiff_string_rabdiff')}

if __name__ == '__main__':  # pragma: no cover
    import glob
    import os
    import pprint
    import re
    import shutil
    import tarfile
    from urllib.request import urlretrieve

    PHP_MANUAL_URL     = 'http://us3.php.net/distributions/manual/php_manual_en.tar.gz'
    PHP_MANUAL_DIR     = './php-chunked-xhtml/'
    PHP_REFERENCE_GLOB = 'ref.*'
    PHP_FUNCTION_RE    = r'<a href="function\..*?\.html">(.*?)</a>'
    PHP_MODULE_RE      = '<title>(.*?) Functions</title>'

    def get_php_functions():
        function_re = re.compile(PHP_FUNCTION_RE)
        module_re   = re.compile(PHP_MODULE_RE)
        modules     = {}

        for file in get_php_references():
            module = ''
            with open(file, encoding='utf-8') as f:
                for line in f:
                    if not module:
                        search = module_re.search(line)
                        if search:
                            module = search.group(1)
                            modules[module] = []

                    elif 'href="function.' in line:
                        for match in function_re.finditer(line):
                            fn = match.group(1)
                            if '»' not in fn and '«' not in fn and \
                               '::' not in fn and '\\' not in fn and \
                               fn not in modules[module]:
                                modules[module].append(fn)

            if module:
                # These are dummy manual pages, not actual functions
                if module == 'Filesystem':
                    modules[module].remove('delete')

                if not modules[module]:
                    del modules[module]

        for key in modules:
            modules[key] = tuple(modules[key])
        return modules

    def get_php_references():
        download = urlretrieve(PHP_MANUAL_URL)
        with tarfile.open(download[0]) as tar:
            tar.extractall()
        yield from glob.glob(f"{PHP_MANUAL_DIR}{PHP_REFERENCE_GLOB}")
        os.remove(download[0])

    def regenerate(filename, modules):
        with open(filename, encoding='utf-8') as fp:
            content = fp.read()

        header = content[:content.find('MODULES = {')]
        footer = content[content.find("if __name__ == '__main__':"):]

        with open(filename, 'w', encoding='utf-8') as fp:
            fp.write(header)
            fp.write(f'MODULES = {pprint.pformat(modules)}\n\n')
            fp.write(footer)

    def run():
        print('>> Downloading Function Index')
        modules = get_php_functions()
        total = sum(len(v) for v in modules.values())
        print('%d functions found' % total)
        regenerate(__file__, modules)
        shutil.rmtree(PHP_MANUAL_DIR)

    run()
