<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0.0000019073486328125 2127.461669921875 689.9356689453125" style="max-width: 2127.461669921875px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e"><style>#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .error-icon{fill:#a44141;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .error-text{fill:#ddd;stroke:#ddd;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edge-thickness-normal{stroke-width:1px;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edge-thickness-thick{stroke-width:3.5px;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edge-pattern-solid{stroke-dasharray:0;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .marker.cross{stroke:lightgrey;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e p{margin:0;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .cluster-label text{fill:#F9FFFE;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .cluster-label span{color:#F9FFFE;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .cluster-label span p{background-color:transparent;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .label text,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e span{fill:#ccc;color:#ccc;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .node rect,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .node circle,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .node ellipse,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .node polygon,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .rough-node .label text,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .node .label text,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .image-shape .label,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .icon-shape .label{text-anchor:middle;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .rough-node .label,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .node .label,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .image-shape .label,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .icon-shape .label{text-align:center;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .node.clickable{cursor:pointer;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .arrowheadPath{fill:lightgrey;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .cluster text{fill:#F9FFFE;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .cluster span{color:#F9FFFE;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e rect.text{fill:none;stroke-width:0;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .icon-shape,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .icon-shape p,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .icon-shape rect,#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="Monitoring" class="cluster"><rect height="103.98928451538086" width="702.2428436279297" y="215.97856903076172" x="8" style=""></rect><g transform="translate(321.2624969482422, 215.97856903076172)" class="cluster-label"><foreignObject height="23.98928451538086" width="75.71784973144531"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Monitoring</p></span></div></foreignObject></g></g><g data-look="classic" id="Configuration" class="cluster"><rect height="103.98928451538086" width="469.73570251464844" y="577.9464225769043" x="606.5392761230469" style=""></rect><g transform="translate(792.9089164733887, 577.9464225769043)" class="cluster-label"><foreignObject height="23.98928451538086" width="96.99642181396484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Configuration</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M571.582,61.989L571.582,66.156C571.582,70.323,571.582,78.656,571.582,86.323C571.582,93.989,571.582,100.989,571.582,104.489L571.582,107.989"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M680.252,145.743L801.466,153.282C922.681,160.821,1165.111,175.9,1286.325,187.606C1407.54,199.312,1407.54,207.645,1407.54,215.312C1407.54,222.979,1407.54,229.979,1407.54,233.479L1407.54,236.979"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M1407.54,294.968L1407.54,299.135C1407.54,303.301,1407.54,311.635,1407.54,319.968C1407.54,328.301,1407.54,336.635,1407.54,344.301C1407.54,351.968,1407.54,358.968,1407.54,362.468L1407.54,365.968"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M1306.446,412.618L1267.334,418.674C1228.223,424.731,1150,436.844,1110.889,446.401C1071.778,455.957,1071.778,462.957,1071.778,466.457L1071.778,469.957"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_F_4" d="M1407.54,423.957L1407.54,428.124C1407.54,432.29,1407.54,440.624,1407.54,448.29C1407.54,455.957,1407.54,462.957,1407.54,466.457L1407.54,469.957"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_G_5" d="M1132.349,527.946L1141.698,532.113C1151.048,536.28,1169.746,544.613,1179.095,552.946C1188.445,561.28,1188.445,569.613,1188.445,577.28C1188.445,584.946,1188.445,591.946,1188.445,595.446L1188.445,598.946"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_H_6" d="M1407.54,527.946L1407.54,532.113C1407.54,536.28,1407.54,544.613,1407.54,552.946C1407.54,561.28,1407.54,569.613,1407.54,577.28C1407.54,584.946,1407.54,591.946,1407.54,595.446L1407.54,598.946"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_I_7" d="M1508.635,409.169L1563.558,415.8C1618.481,422.432,1728.326,435.694,1783.249,445.826C1838.172,455.957,1838.172,462.957,1838.172,466.457L1838.172,469.957"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_8" d="M1748.558,522.967L1728.219,527.964C1707.881,532.96,1667.203,542.953,1646.865,552.117C1626.526,561.28,1626.526,569.613,1626.526,577.28C1626.526,584.946,1626.526,591.946,1626.526,595.446L1626.526,598.946"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_K_9" d="M1838.172,527.946L1838.172,532.113C1838.172,536.28,1838.172,544.613,1838.172,552.946C1838.172,561.28,1838.172,569.613,1838.172,577.28C1838.172,584.946,1838.172,591.946,1838.172,595.446L1838.172,598.946"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_L_10" d="M1927.787,523.798L1946.842,528.656C1965.898,533.514,2004.009,543.23,2023.065,552.255C2042.12,561.28,2042.12,569.613,2042.12,577.28C2042.12,584.946,2042.12,591.946,2042.12,595.446L2042.12,598.946"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_M_11" d="M653.956,165.979L666.67,170.145C679.385,174.312,704.814,182.645,717.528,190.979C730.243,199.312,730.243,207.645,730.243,220.478C730.243,233.31,730.243,250.642,730.243,267.973C730.243,285.305,730.243,302.636,730.243,315.469C730.243,328.301,730.243,336.635,730.243,349.467C730.243,362.299,730.243,379.631,730.243,396.962C730.243,414.294,730.243,431.626,730.243,448.957C730.243,466.289,730.243,483.62,730.243,500.952C730.243,518.283,730.243,535.615,730.243,548.447C730.243,561.28,730.243,569.613,730.243,577.28C730.243,584.946,730.243,591.946,730.243,595.446L730.243,598.946"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_N_12" d="M1011.206,527.946L1001.857,532.113C992.508,536.28,973.809,544.613,964.46,552.946C955.111,561.28,955.111,569.613,955.111,577.28C955.111,584.946,955.111,591.946,955.111,595.446L955.111,598.946"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_O_13" d="M462.912,151.625L406.527,158.184C350.142,164.743,237.371,177.861,180.985,188.586C124.6,199.312,124.6,207.645,124.6,215.312C124.6,222.979,124.6,229.979,124.6,233.479L124.6,236.979"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_P_14" d="M462.912,163.077L441.937,167.727C420.962,172.377,379.011,181.678,358.036,190.495C337.061,199.312,337.061,207.645,337.061,215.312C337.061,222.979,337.061,229.979,337.061,233.479L337.061,236.979"></path><path marker-end="url(#mermaid-d37a6c34-7486-49da-bfe1-d4612c5d910e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_Q_15" d="M571.582,165.979L571.582,170.145C571.582,174.312,571.582,182.645,571.582,190.979C571.582,199.312,571.582,207.645,571.582,215.312C571.582,222.979,571.582,229.979,571.582,233.479L571.582,236.979"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(571.5821304321289, 34.99464225769043)" id="flowchart-A-0" class="node default"><rect height="53.98928451538086" width="155.49642181396484" y="-26.99464225769043" x="-77.74821090698242" style="" class="basic label-container"></rect><g transform="translate(-47.74821090698242, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="95.49642181396484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Kafka Stream</p></span></div></foreignObject></g></g><g transform="translate(571.5821304321289, 138.9839267730713)" id="flowchart-B-1" class="node default"><rect height="53.98928451538086" width="217.3392791748047" y="-26.99464225769043" x="-108.66963958740234" style="" class="basic label-container"></rect><g transform="translate(-78.66963958740234, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="157.3392791748047"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SparkStreamProcessor</p></span></div></foreignObject></g></g><g transform="translate(1407.5401496887207, 267.97321128845215)" id="flowchart-C-3" class="node default"><rect height="53.98928451538086" width="145.3499984741211" y="-26.99464225769043" x="-72.67499923706055" style="" class="basic label-container"></rect><g transform="translate(-42.67499923706055, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="85.3499984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>JSON Parser</p></span></div></foreignObject></g></g><g transform="translate(1407.5401496887207, 396.962495803833)" id="flowchart-D-5" class="node default"><rect height="53.98928451538086" width="202.18927001953125" y="-26.99464225769043" x="-101.09463500976562" style="" class="basic label-container"></rect><g transform="translate(-71.09463500976562, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="142.18927001953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windowed Analytics</p></span></div></foreignObject></g></g><g transform="translate(1071.7776565551758, 500.95178031921387)" id="flowchart-E-7" class="node default"><rect height="53.98928451538086" width="155.3249969482422" y="-26.99464225769043" x="-77.6624984741211" style="" class="basic label-container"></rect><g transform="translate(-47.662498474121094, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="95.32499694824219"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AlertManager</p></span></div></foreignObject></g></g><g transform="translate(1407.5401496887207, 500.95178031921387)" id="flowchart-F-9" class="node default"><rect height="53.98928451538086" width="198.8249969482422" y="-26.99464225769043" x="-99.4124984741211" style="" class="basic label-container"></rect><g transform="translate(-69.4124984741211, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="138.8249969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>DataQualityMonitor</p></span></div></foreignObject></g></g><g transform="translate(1188.4446182250977, 629.9410648345947)" id="flowchart-G-11" class="node default"><rect height="53.98928451538086" width="154.3392791748047" y="-26.99464225769043" x="-77.16963958740234" style="" class="basic label-container"></rect><g transform="translate(-47.169639587402344, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="94.33927917480469"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert Logging</p></span></div></foreignObject></g></g><g transform="translate(1407.5401496887207, 629.9410648345947)" id="flowchart-H-13" class="node default"><rect height="53.98928451538086" width="167.89285278320312" y="-26.99464225769043" x="-83.94642639160156" style="" class="basic label-container"></rect><g transform="translate(-53.94642639160156, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="107.89285278320312"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Quality Metrics</p></span></div></foreignObject></g></g><g transform="translate(1838.1722831726074, 500.95178031921387)" id="flowchart-I-15" class="node default"><rect height="53.98928451538086" width="179.2285614013672" y="-26.99464225769043" x="-89.6142807006836" style="" class="basic label-container"></rect><g transform="translate(-59.614280700683594, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="119.22856140136719"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Multiple Outputs</p></span></div></foreignObject></g></g><g transform="translate(1626.5258598327637, 629.9410648345947)" id="flowchart-J-17" class="node default"><rect height="53.98928451538086" width="170.0785675048828" y="-26.99464225769043" x="-85.0392837524414" style="" class="basic label-container"></rect><g transform="translate(-55.039283752441406, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="110.07856750488281"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Console Output</p></span></div></foreignObject></g></g><g transform="translate(1838.1722831726074, 629.9410648345947)" id="flowchart-K-19" class="node default"><rect height="53.98928451538086" width="153.2142791748047" y="-26.99464225769043" x="-76.60713958740234" style="" class="basic label-container"></rect><g transform="translate(-46.607139587402344, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="93.21427917480469"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Parquet Files</p></span></div></foreignObject></g></g><g transform="translate(2042.1204948425293, 629.9410648345947)" id="flowchart-L-21" class="node default"><rect height="53.98928451538086" width="154.68213653564453" y="-26.99464225769043" x="-77.34106826782227" style="" class="basic label-container"></rect><g transform="translate(-47.341068267822266, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="94.68213653564453"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Kafka Output</p></span></div></foreignObject></g></g><g transform="translate(730.2428436279297, 629.9410648345947)" id="flowchart-M-22" class="node default"><rect height="53.98928451538086" width="177.40713500976562" y="-26.99464225769043" x="-88.70356750488281" style="" class="basic label-container"></rect><g transform="translate(-58.70356750488281, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="117.40713500976562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>StreamingConfig</p></span></div></foreignObject></g></g><g transform="translate(955.1106948852539, 629.9410648345947)" id="flowchart-N-23" class="node default"><rect height="53.98928451538086" width="172.3285675048828" y="-26.99464225769043" x="-86.1642837524414" style="" class="basic label-container"></rect><g transform="translate(-56.164283752441406, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="112.32856750488281"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AlertThresholds</p></span></div></foreignObject></g></g><g transform="translate(124.5999984741211, 267.97321128845215)" id="flowchart-O-28" class="node default"><rect height="53.98928451538086" width="163.1999969482422" y="-26.99464225769043" x="-81.5999984741211" style="" class="basic label-container"></rect><g transform="translate(-51.599998474121094, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="103.19999694824219"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Spark UI :4040</p></span></div></foreignObject></g></g><g transform="translate(337.06070709228516, 267.97321128845215)" id="flowchart-P-29" class="node default"><rect height="53.98928451538086" width="161.72142028808594" y="-26.99464225769043" x="-80.86071014404297" style="" class="basic label-container"></rect><g transform="translate(-50.86071014404297, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="101.72142028808594"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Health Checks</p></span></div></foreignObject></g></g><g transform="translate(571.5821304321289, 267.97321128845215)" id="flowchart-Q-30" class="node default"><rect height="53.98928451538086" width="207.32142639160156" y="-26.99464225769043" x="-103.66071319580078" style="" class="basic label-container"></rect><g transform="translate(-73.66071319580078, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="147.32142639160156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Performance Metrics</p></span></div></foreignObject></g></g></g></g></g></svg>