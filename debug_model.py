#!/usr/bin/env python3
"""
Debug script to check model files and fix loading issues.
"""

import os
import pickle
import sys
from pathlib import Path


def check_model_file(model_path):
    """Check model file and diagnose issues."""
    print(f"🔍 Checking model file: {model_path}")

    # Check if file exists
    if not os.path.exists(model_path):
        print(f"❌ File does not exist: {model_path}")
        return False

    # Check file size
    file_size = os.path.getsize(model_path)
    print(f"📏 File size: {file_size} bytes")

    if file_size == 0:
        print(f"❌ File is empty!")
        return False

    # Try to load the pickle file
    try:
        print(f"📦 Attempting to load pickle file...")
        with open(model_path, "rb") as f:
            model_package = pickle.load(f)

        print(f"✅ Pickle file loaded successfully!")

        # Check contents
        print(f"📋 Model package contents:")
        for key, value in model_package.items():
            print(f"  - {key}: {type(value)}")

        # Check model specifically
        if "model" in model_package:
            model = model_package["model"]
            print(f"🤖 Model type: {type(model)}")

            # Check if model has required methods
            if hasattr(model, "predict"):
                print(f"✅ Model has predict method")
            else:
                print(f"❌ Model missing predict method")

            if hasattr(model, "predict_proba"):
                print(f"✅ Model has predict_proba method")
            else:
                print(f"❌ Model missing predict_proba method")

        return True

    except EOFError as e:
        print(f"❌ EOFError (file truncated or corrupted): {e}")
        return False
    except pickle.UnpicklingError as e:
        print(f"❌ Pickle error (invalid format): {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def find_model_files():
    """Find all model files in the project."""
    print(f"🔍 Searching for model files...")

    # Check both host and Docker paths
    model_paths = [
        "models/smoke_detection_model.pkl",
        "ml/models/best_model.pkl",
        "ml/models/smoke_detection_model.pkl",
        "/app/models/smoke_detection_model.pkl",  # Docker path
        "/app/ml/models/best_model.pkl",  # Docker path
        "/app/ml/models/smoke_detection_model.pkl",  # Docker path
    ]

    # Also search for timestamped models in both locations
    search_dirs = ["ml/models", "/app/ml/models"]
    for search_dir in search_dirs:
        ml_models_dir = Path(search_dir)
        if ml_models_dir.exists():
            timestamped_models = list(ml_models_dir.glob("smoke_detection_model_*.pkl"))
            model_paths.extend([str(p) for p in timestamped_models])

            # Also look for any .pkl files
            all_pkl_files = list(ml_models_dir.glob("*.pkl"))
            model_paths.extend([str(p) for p in all_pkl_files])

    # Remove duplicates
    model_paths = list(set(model_paths))

    found_models = []
    for path in model_paths:
        if os.path.exists(path):
            found_models.append(path)
            print(f"✅ Found: {path}")
        else:
            print(f"❌ Not found: {path}")

    return found_models


def copy_working_model():
    """Copy a working model to the expected location."""
    print(f"🔄 Attempting to copy working model...")

    # Find all models
    found_models = find_model_files()

    expected_path = "models/smoke_detection_model.pkl"

    # Check if expected model already works
    if expected_path in found_models:
        if check_model_file(expected_path):
            print(f"✅ Expected model is already working!")
            return True

    # Try to find a working model
    for model_path in found_models:
        if model_path != expected_path:
            print(f"🧪 Testing model: {model_path}")
            if check_model_file(model_path):
                print(f"✅ Found working model: {model_path}")

                # Copy to expected location
                try:
                    import shutil

                    os.makedirs(os.path.dirname(expected_path), exist_ok=True)
                    shutil.copy2(model_path, expected_path)
                    print(f"✅ Copied to: {expected_path}")

                    # Verify copy
                    if check_model_file(expected_path):
                        print(f"✅ Copy verified successfully!")
                        return True
                    else:
                        print(f"❌ Copy verification failed!")

                except Exception as e:
                    print(f"❌ Copy failed: {e}")

    print(f"❌ No working model found!")
    return False


def main():
    """Main debug function."""
    print(f"🔥 IoT Smoke Detection - Model Debug Tool")
    print(f"=" * 50)

    # Find all model files
    found_models = find_model_files()

    if not found_models:
        print(f"❌ No model files found!")
        print(f"💡 Make sure ML training has completed successfully.")
        return

    print(f"\n📋 Testing all found models:")
    print(f"=" * 30)

    working_models = []
    for model_path in found_models:
        print(f"\n🧪 Testing: {model_path}")
        if check_model_file(model_path):
            working_models.append(model_path)
            print(f"✅ WORKING")
        else:
            print(f"❌ BROKEN")

    print(f"\n📊 Summary:")
    print(f"  Total models found: {len(found_models)}")
    print(f"  Working models: {len(working_models)}")
    print(f"  Broken models: {len(found_models) - len(working_models)}")

    if working_models:
        print(f"\n✅ Working models:")
        for model in working_models:
            print(f"  - {model}")

        # Try to fix the expected model path
        expected_path = "models/smoke_detection_model.pkl"
        if expected_path not in working_models:
            print(f"\n🔧 Fixing expected model path...")
            copy_working_model()
    else:
        print(f"\n❌ No working models found!")
        print(f"💡 You may need to retrain the model.")


if __name__ == "__main__":
    main()
