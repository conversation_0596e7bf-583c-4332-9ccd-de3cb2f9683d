# Dockerfile for Flask API Backend
# Production-ready Flask container for IoT smoke detection API

FROM python:3.10-slim

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=app.api.prediction_api
ENV FLASK_ENV=production

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt /app/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p /app/logs \
    && mkdir -p /app/ml/models \
    && mkdir -p /app/data

# Copy application code
COPY app/ /app/app/
COPY config/ /app/config/
COPY ml/ /app/ml/
COPY data/ /app/data/

# Create non-root user
RUN groupadd -r apiuser || true
RUN useradd -r -g apiuser apiuser || true
RUN chown -R apiuser:apiuser /app || true

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Switch to non-root user
USER apiuser

# Expose Flask API port
EXPOSE 5000

# Default command - start Flask API server
CMD ["python", "-m", "app.api.prediction_api", "--host", "0.0.0.0", "--port", "5000"]
