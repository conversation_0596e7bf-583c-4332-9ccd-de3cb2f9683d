<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0.0000019073486328125 1783.1151123046875 1026.903564453125" style="max-width: 1783.1151123046875px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-42d8edf8-222a-4412-990f-16919c88e33c"><style>#mermaid-42d8edf8-222a-4412-990f-16919c88e33c{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .error-icon{fill:#a44141;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .error-text{fill:#ddd;stroke:#ddd;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edge-thickness-normal{stroke-width:1px;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edge-thickness-thick{stroke-width:3.5px;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edge-pattern-solid{stroke-dasharray:0;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .marker.cross{stroke:lightgrey;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c p{margin:0;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .cluster-label text{fill:#F9FFFE;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .cluster-label span{color:#F9FFFE;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .cluster-label span p{background-color:transparent;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .label text,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c span{fill:#ccc;color:#ccc;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .node rect,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .node circle,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .node ellipse,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .node polygon,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .rough-node .label text,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .node .label text,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .image-shape .label,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .icon-shape .label{text-anchor:middle;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .rough-node .label,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .node .label,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .image-shape .label,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .icon-shape .label{text-align:center;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .node.clickable{cursor:pointer;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .arrowheadPath{fill:lightgrey;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .cluster text{fill:#F9FFFE;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .cluster span{color:#F9FFFE;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c rect.text{fill:none;stroke-width:0;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .icon-shape,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .icon-shape p,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .icon-shape rect,#mermaid-42d8edf8-222a-4412-990f-16919c88e33c .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-42d8edf8-222a-4412-990f-16919c88e33c :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="Outputs" class="cluster"><rect height="103.98928451538086" width="1295.365146636963" y="914.9142761230469" x="15.242843627929688" style=""></rect><g transform="translate(634.7575607299805, 914.9142761230469)" class="cluster-label"><foreignObject height="23.98928451538086" width="56.33571243286133"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Outputs</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="440.95713806152344" width="294.1321105957031" y="423.95713806152344" x="8" style=""></rect><g transform="translate(81.57141876220703, 423.95713806152344)" class="cluster-label"><foreignObject height="23.98928451538086" width="146.98927307128906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Historical Processing</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="207.97856903076172" width="507.33213806152344" y="423.95713806152344" x="1267.7829933166504" style=""></rect><g transform="translate(1465.6276359558105, 423.95713806152344)" class="cluster-label"><foreignObject height="23.98928451538086" width="111.64285278320312"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML Components</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M787.341,61.989L787.341,66.156C787.341,70.323,787.341,78.656,787.341,86.323C787.341,93.989,787.341,100.989,787.341,104.489L787.341,107.989"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M787.341,165.979L787.341,170.145C787.341,174.312,787.341,182.645,787.341,190.312C787.341,197.979,787.341,204.979,787.341,208.479L787.341,211.979"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M686.246,258.158L645.402,264.293C604.557,270.428,522.868,282.698,482.023,297.499C441.179,312.299,441.179,329.631,441.179,346.962C441.179,364.294,441.179,381.626,441.179,394.458C441.179,407.29,441.179,415.624,441.179,423.29C441.179,430.957,441.179,437.957,441.179,441.457L441.179,444.957"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E_3" d="M888.436,266.164L909.362,270.965C930.289,275.766,972.142,285.367,993.069,293.667C1013.995,301.968,1013.995,308.968,1013.995,312.468L1013.995,315.968"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M941.438,357.819L895.615,364.675C849.791,371.532,758.144,385.244,712.32,396.267C666.496,407.29,666.496,415.624,666.496,423.29C666.496,430.957,666.496,437.957,666.496,441.457L666.496,444.957"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_G_5" d="M951.949,373.957L942.372,378.124C932.795,382.29,913.641,390.624,904.064,398.957C894.487,407.29,894.487,415.624,894.487,423.29C894.487,430.957,894.487,437.957,894.487,441.457L894.487,444.957"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_H_6" d="M1076.042,373.957L1085.619,378.124C1095.196,382.29,1114.35,390.624,1123.927,398.957C1133.504,407.29,1133.504,415.624,1133.504,423.29C1133.504,430.957,1133.504,437.957,1133.504,441.457L1133.504,444.957"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_I_7" d="M441.179,502.946L441.179,507.113C441.179,511.28,441.179,519.613,441.179,527.28C441.179,534.946,441.179,541.946,441.179,545.446L441.179,548.946"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_J_8" d="M1041.254,498.375L1020.977,503.304C1000.701,508.232,960.149,518.089,939.873,526.518C919.596,534.946,919.596,541.946,919.596,545.446L919.596,548.946"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_K_9" d="M441.179,606.936L441.179,611.102C441.179,615.269,441.179,623.602,441.179,631.936C441.179,640.269,441.179,648.602,451.157,656.692C461.135,664.781,481.091,672.627,491.069,676.549L501.047,680.472"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_10" d="M919.596,606.936L919.596,611.102C919.596,615.269,919.596,623.602,919.596,631.936C919.596,640.269,919.596,648.602,875.907,659.331C832.217,670.06,744.837,683.185,701.147,689.747L657.457,696.31"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_11" d="M509.937,735.925L500.136,740.092C490.335,744.258,470.734,752.592,460.933,760.258C451.132,767.925,451.132,774.925,451.132,778.425L451.132,781.925"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_M_12" d="M1086.553,354.495L1157.936,361.905C1229.319,369.316,1372.085,384.136,1443.469,395.713C1514.852,407.29,1514.852,415.624,1514.852,423.29C1514.852,430.957,1514.852,437.957,1514.852,441.457L1514.852,444.957"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_13" d="M1455.761,502.946L1446.64,507.113C1437.519,511.28,1419.277,519.613,1410.156,527.28C1401.036,534.946,1401.036,541.946,1401.036,545.446L1401.036,548.946"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_O_14" d="M1573.943,502.946L1583.064,507.113C1592.185,511.28,1610.426,519.613,1619.547,527.28C1628.668,534.946,1628.668,541.946,1628.668,545.446L1628.668,548.946"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_15" d="M155.066,502.946L155.066,507.113C155.066,511.28,155.066,519.613,155.066,527.28C155.066,534.946,155.066,541.946,155.066,545.446L155.066,548.946"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_16" d="M155.066,606.936L155.066,611.102C155.066,615.269,155.066,623.602,155.066,631.936C155.066,640.269,155.066,648.602,155.066,656.269C155.066,663.936,155.066,670.936,155.066,674.436L155.066,677.936"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_S_17" d="M155.066,735.925L155.066,740.092C155.066,744.258,155.066,752.592,155.066,760.258C155.066,767.925,155.066,774.925,155.066,778.425L155.066,781.925"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_T_18" d="M451.132,839.914L451.132,844.081C451.132,848.248,451.132,856.581,451.132,864.914C451.132,873.248,451.132,881.581,451.132,889.914C451.132,898.248,451.132,906.581,451.132,914.248C451.132,921.914,451.132,928.914,451.132,932.414L451.132,935.914"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_U_19" d="M1164.994,502.946L1169.855,507.113C1174.715,511.28,1184.437,519.613,1189.297,532.446C1194.158,545.278,1194.158,562.61,1194.158,579.941C1194.158,597.273,1194.158,614.604,1194.158,627.437C1194.158,640.269,1194.158,648.602,1194.158,661.435C1194.158,674.267,1194.158,691.599,1194.158,708.93C1194.158,726.262,1194.158,743.593,1194.158,760.925C1194.158,778.257,1194.158,795.588,1194.158,812.92C1194.158,830.251,1194.158,847.583,1194.158,860.415C1194.158,873.248,1194.158,881.581,1194.158,889.914C1194.158,898.248,1194.158,906.581,1194.158,914.248C1194.158,921.914,1194.158,928.914,1194.158,932.414L1194.158,935.914"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_V_20" d="M155.066,839.914L155.066,844.081C155.066,848.248,155.066,856.581,155.066,864.914C155.066,873.248,155.066,881.581,155.066,889.914C155.066,898.248,155.066,906.581,155.066,914.248C155.066,921.914,155.066,928.914,155.066,932.414L155.066,935.914"></path><path marker-end="url(#mermaid-42d8edf8-222a-4412-990f-16919c88e33c_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_W_21" d="M653.502,723.128L689.028,729.428C724.554,735.727,795.605,748.326,831.131,763.291C866.657,778.257,866.657,795.588,866.657,812.92C866.657,830.251,866.657,847.583,866.657,860.415C866.657,873.248,866.657,881.581,866.657,889.914C866.657,898.248,866.657,906.581,866.657,914.248C866.657,921.914,866.657,928.914,866.657,932.414L866.657,935.914"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(787.3410339355469, 34.99464225769043)" id="flowchart-A-74" class="node default"><rect height="53.98928451538086" width="155.49642181396484" y="-26.99464225769043" x="-77.74821090698242" style="" class="basic label-container"></rect><g transform="translate(-47.74821090698242, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="95.49642181396484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Kafka Stream</p></span></div></foreignObject></g></g><g transform="translate(787.3410339355469, 138.9839267730713)" id="flowchart-B-75" class="node default"><rect height="53.98928451538086" width="176.12142181396484" y="-26.99464225769043" x="-88.06071090698242" style="" class="basic label-container"></rect><g transform="translate(-58.06071090698242, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="116.12142181396484"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Spark Streaming</p></span></div></foreignObject></g></g><g transform="translate(787.3410339355469, 242.97321128845215)" id="flowchart-C-77" class="node default"><rect height="53.98928451538086" width="202.18927001953125" y="-26.99464225769043" x="-101.09463500976562" style="" class="basic label-container"></rect><g transform="translate(-71.09463500976562, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="142.18927001953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Windowed Analytics</p></span></div></foreignObject></g></g><g transform="translate(441.17853927612305, 475.95178031921387)" id="flowchart-D-79" class="node default"><rect height="53.98928451538086" width="188.18569946289062" y="-26.99464225769043" x="-94.09284973144531" style="" class="basic label-container"></rect><g transform="translate(-64.09284973144531, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="128.18569946289062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Statistical Metrics</p></span></div></foreignObject></g></g><g transform="translate(1013.9954948425293, 346.962495803833)" id="flowchart-E-81" class="node default"><rect height="53.98928451538086" width="145.1142807006836" y="-26.99464225769043" x="-72.5571403503418" style="" class="basic label-container"></rect><g transform="translate(-42.5571403503418, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="85.1142807006836"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MLPredictor</p></span></div></foreignObject></g></g><g transform="translate(666.496395111084, 475.95178031921387)" id="flowchart-F-83" class="node default"><rect height="53.98928451538086" width="162.4499969482422" y="-26.99464225769043" x="-81.2249984741211" style="" class="basic label-container"></rect><g transform="translate(-51.224998474121094, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="102.44999694824219"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Model Loading</p></span></div></foreignObject></g></g><g transform="translate(894.4874610900879, 475.95178031921387)" id="flowchart-G-85" class="node default"><rect height="53.98928451538086" width="193.53213500976562" y="-26.99464225769043" x="-96.76606750488281" style="" class="basic label-container"></rect><g transform="translate(-66.76606750488281, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="133.53213500976562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Prediction Caching</p></span></div></foreignObject></g></g><g transform="translate(1133.5035285949707, 475.95178031921387)" id="flowchart-H-87" class="node default"><rect height="53.98928451538086" width="184.49999237060547" y="-26.99464225769043" x="-92.24999618530273" style="" class="basic label-container"></rect><g transform="translate(-62.249996185302734, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="124.49999237060547"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Batch Predictions</p></span></div></foreignObject></g></g><g transform="translate(441.17853927612305, 579.9410648345947)" id="flowchart-I-89" class="node default"><rect height="53.98928451538086" width="176.26070404052734" y="-26.99464225769043" x="-88.13035202026367" style="" class="basic label-container"></rect><g transform="translate(-58.13035202026367, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="116.26070404052734"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Threshold Alerts</p></span></div></foreignObject></g></g><g transform="translate(919.5963859558105, 579.9410648345947)" id="flowchart-J-91" class="node default"><rect height="53.98928451538086" width="172.61785125732422" y="-26.99464225769043" x="-86.30892562866211" style="" class="basic label-container"></rect><g transform="translate(-56.30892562866211, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="112.61785125732422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML-based Alerts</p></span></div></foreignObject></g></g><g transform="translate(573.4338912963867, 708.9303493499756)" id="flowchart-K-93" class="node default"><rect height="53.98928451538086" width="160.13571166992188" y="-26.99464225769043" x="-80.06785583496094" style="" class="basic label-container"></rect><g transform="translate(-50.06785583496094, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="100.13571166992188"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert Manager</p></span></div></foreignObject></g></g><g transform="translate(451.1321105957031, 812.9196338653564)" id="flowchart-L-97" class="node default"><rect height="53.98928451538086" width="227.99998474121094" y="-26.99464225769043" x="-113.99999237060547" style="" class="basic label-container"></rect><g transform="translate(-83.99999237060547, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="167.99998474121094"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Comprehensive Logging</p></span></div></foreignObject></g></g><g transform="translate(1514.8517417907715, 475.95178031921387)" id="flowchart-M-98" class="node default"><rect height="53.98928451538086" width="241.76785278320312" y="-26.99464225769043" x="-120.88392639160156" style="" class="basic label-container"></rect><g transform="translate(-90.88392639160156, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="181.76785278320312"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SmokeDetectionPredictor</p></span></div></foreignObject></g></g><g transform="translate(1401.0356712341309, 579.9410648345947)" id="flowchart-N-99" class="node default"><rect height="53.98928451538086" width="151.24285125732422" y="-26.99464225769043" x="-75.62142562866211" style="" class="basic label-container"></rect><g transform="translate(-45.62142562866211, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="91.24285125732422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ModelLoader</p></span></div></foreignObject></g></g><g transform="translate(1628.667812347412, 579.9410648345947)" id="flowchart-O-100" class="node default"><rect height="53.98928451538086" width="204.02142333984375" y="-26.99464225769043" x="-102.01071166992188" style="" class="basic label-container"></rect><g transform="translate(-72.01071166992188, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="144.02142333984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Feature Engineering</p></span></div></foreignObject></g></g><g transform="translate(155.06605529785156, 475.95178031921387)" id="flowchart-P-107" class="node default"><rect height="53.98928451538086" width="224.1321258544922" y="-26.99464225769043" x="-112.0660629272461" style="" class="basic label-container"></rect><g transform="translate(-82.0660629272461, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="164.1321258544922"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Historical ML Processor</p></span></div></foreignObject></g></g><g transform="translate(155.06605529785156, 579.9410648345947)" id="flowchart-Q-108" class="node default"><rect height="53.98928451538086" width="179.19641876220703" y="-26.99464225769043" x="-89.59820938110352" style="" class="basic label-container"></rect><g transform="translate(-59.598209381103516, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="119.19641876220703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Batch Processing</p></span></div></foreignObject></g></g><g transform="translate(155.06605529785156, 708.9303493499756)" id="flowchart-R-109" class="node default"><rect height="53.98928451538086" width="207.32142639160156" y="-26.99464225769043" x="-103.66071319580078" style="" class="basic label-container"></rect><g transform="translate(-73.66071319580078, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="147.32142639160156"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Performance Metrics</p></span></div></foreignObject></g></g><g transform="translate(155.06605529785156, 812.9196338653564)" id="flowchart-S-110" class="node default"><rect height="53.98928451538086" width="192.1392822265625" y="-26.99464225769043" x="-96.06964111328125" style="" class="basic label-container"></rect><g transform="translate(-66.06964111328125, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="132.1392822265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Report Generation</p></span></div></foreignObject></g></g><g transform="translate(451.1321105957031, 966.9089183807373)" id="flowchart-T-117" class="node default"><rect height="53.98928451538086" width="197.7321319580078" y="-26.99464225769043" x="-98.8660659790039" style="" class="basic label-container"></rect><g transform="translate(-68.8660659790039, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="137.7321319580078"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Real-time Analytics</p></span></div></foreignObject></g></g><g transform="translate(1194.1579933166504, 966.9089183807373)" id="flowchart-U-118" class="node default"><rect height="53.98928451538086" width="162.89999389648438" y="-26.99464225769043" x="-81.44999694824219" style="" class="basic label-container"></rect><g transform="translate(-51.44999694824219, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="102.89999389648438"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ML Predictions</p></span></div></foreignObject></g></g><g transform="translate(155.06605529785156, 966.9089183807373)" id="flowchart-V-119" class="node default"><rect height="53.98928451538086" width="209.64642333984375" y="-26.99464225769043" x="-104.82321166992188" style="" class="basic label-container"></rect><g transform="translate(-74.82321166992188, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="149.64642333984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Performance Reports</p></span></div></foreignObject></g></g><g transform="translate(866.6571044921875, 966.9089183807373)" id="flowchart-W-120" class="node default"><rect height="53.98928451538086" width="191.47499084472656" y="-26.99464225769043" x="-95.73749542236328" style="" class="basic label-container"></rect><g transform="translate(-65.73749542236328, -11.99464225769043)" style="" class="label"><rect></rect><foreignObject height="23.98928451538086" width="131.47499084472656"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Alert Notifications</p></span></div></foreignObject></g></g></g></g></g></svg>