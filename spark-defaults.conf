# Spark Configuration for IoT Smoke Detection Stream Processing
# Optimized for real-time analytics and Kafka integration

# Application Configuration
spark.app.name                     IoTSmokeDetectionStreamProcessor
spark.master                       local[*]

# Memory Configuration
spark.driver.memory                2g
spark.driver.maxResultSize         1g
spark.executor.memory              2g
spark.executor.cores               2

# Streaming Configuration
spark.sql.streaming.checkpointLocation    /app/data/checkpoints
spark.sql.streaming.metricsEnabled        true
spark.sql.streaming.ui.enabled             true
spark.sql.streaming.ui.retainedBatches     100
spark.sql.streaming.ui.retainedQueries     100

# Kafka Integration
spark.sql.streaming.kafka.useDeprecatedOffsetFetching    false
spark.sql.streaming.kafka.includeHeaders                 true

# Performance Optimization
spark.sql.adaptive.enabled                    true
spark.sql.adaptive.coalescePartitions.enabled true
spark.sql.adaptive.skewJoin.enabled           true
spark.sql.adaptive.localShuffleReader.enabled true
spark.sql.adaptive.advisoryPartitionSizeInBytes    128MB

# Serialization
spark.serializer                    org.apache.spark.serializer.KryoSerializer
spark.kryo.registrationRequired     false

# Shuffle Configuration
spark.sql.shuffle.partitions        200
spark.shuffle.compress              true
spark.shuffle.spill.compress        true

# Dynamic Allocation (disabled for streaming)
spark.dynamicAllocation.enabled     false

# Logging Configuration
spark.eventLog.enabled              true
spark.eventLog.dir                  /app/logs/spark-events
spark.history.fs.logDirectory       /app/logs/spark-events

# UI Configuration
spark.ui.enabled                    true
spark.ui.port                       4040
spark.ui.retainedJobs               100
spark.ui.retainedStages             100

# Network Configuration
spark.network.timeout               120s
spark.rpc.askTimeout                120s
spark.rpc.lookupTimeout             120s

# Checkpoint Configuration
spark.sql.streaming.checkpointLocation.deleteOnStop    false

# Arrow Configuration (for better pandas integration)
spark.sql.execution.arrow.pyspark.enabled              true
spark.sql.execution.arrow.maxRecordsPerBatch           10000

# Parquet Configuration
spark.sql.parquet.compression.codec                     snappy
spark.sql.parquet.enableVectorizedReader               true

# Security (basic configuration)
spark.authenticate                  false
spark.network.crypto.enabled       false

# Resource Management
spark.task.cpus                     1
spark.task.maxFailures              3
spark.stage.maxConsecutiveAttempts  8

# Garbage Collection
spark.executor.extraJavaOptions     -XX:+UseG1GC -XX:+UnlockDiagnosticVMOptions -XX:+G1PrintRegionRememberedSetInfo
spark.driver.extraJavaOptions       -XX:+UseG1GC

# Metrics Configuration
spark.metrics.conf.driver.source.jvm.class             org.apache.spark.metrics.source.JvmSource
spark.metrics.conf.executor.source.jvm.class           org.apache.spark.metrics.source.JvmSource

# SQL Configuration
spark.sql.adaptive.maxShuffledHashJoinLocalMapThreshold 0
spark.sql.join.preferSortMergeJoin                      true

# Streaming Specific
spark.sql.streaming.stateStore.providerClass           org.apache.spark.sql.execution.streaming.state.HDFSBackedStateStoreProvider
spark.sql.streaming.minBatchesToRetain                 100
spark.sql.streaming.stopGracefullyOnShutdown           true

# File System Configuration
spark.hadoop.fs.defaultFS           file:///
spark.sql.warehouse.dir             /app/data/warehouse

# Python Configuration
spark.pyspark.python                python3
spark.pyspark.driver.python         python3
