[tool:pytest]
# Pytest configuration for IoT Smoke Detection Pipeline

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    ml: Machine learning related tests
    stream: Stream processing tests
    kafka: Tests requiring Kafka
    api: Tests requiring Flask API
    flask: Comprehensive Flask API tests
    spark: Tests requiring Spark
    performance: Performance tests
    ml_integration: ML integration tests
    ml_performance: ML performance tests

# Output options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --showlocals
    --durations=10
    --color=yes

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Timeout
timeout = 300
timeout_method = thread

# Coverage (if pytest-cov is installed)
# addopts = --cov=data_processing --cov=app --cov-report=html --cov-report=term-missing

# Parallel execution (if pytest-xdist is installed)
# addopts = -n auto

# HTML reports (if pytest-html is installed)
# addopts = --html=tests/reports/report.html --self-contained-html
