services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    # platform: linux/amd64
    container_name: smoke_zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: ${ZOOKEEPER_CLIENT_PORT}
      ZOOKEEPER_TICK_TIME: 2000  # 2 seconds
    ports:
      - "${ZO<PERSON>EEPER_CLIENT_PORT}:${ZOOKEEPER_CLIENT_PORT}"

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    platform: linux/amd64
    container_name: smoke_kafka
    depends_on:
      - zookeeper
    ports:
      - "${KAFKA_PORT}:${KAFKA_PORT}"
    environment:
      KAFKA_BROKER_ID: ${KAFKA_BROKER_ID}
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:${ZOOKEEPER_CLIENT_PORT}
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:${KAFKA_PORT}
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:${KAFKA_PORT}
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_CREATE_TOPICS: ${KAFKA_TOPIC_SMOKE}:1:1   # topic: partitions: replicas
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true" # 1 partition and 1 relica by default; need to set it false later
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics --bootstrap-server localhost:${KAFKA_PORT} --list"]
      interval: 10s
      timeout: 5s
      retries: 5

  smoke_kafka_producer:
    build:
      context: .
      dockerfile: docker/Dockerfile.ingestion
    container_name: kafka_producer
    env_file:
      - .env
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      KAFKA_BOOTSTRAP_SERVERS: kafka:${KAFKA_PORT}
      KAFKA_TOPIC_SMOKE: ${KAFKA_TOPIC_SMOKE}
    command: >
      sh -c "
        echo 'Waiting for Kafka to be ready...' &&
        sleep 30 &&
        python data_ingestion/stream/kafka_producer.py
      "
    volumes:
      - ./data:/app/data
    restart: unless-stopped

  stream_processor:
    build:
      context: .
      dockerfile: docker/Dockerfile.stream
    container_name: stream_processor
    depends_on:
      - kafka
    environment:
      KAFKA_BOOTSTRAP_SERVERS: kafka:${KAFKA_PORT}
      KAFKA_TOPIC_SMOKE: ${KAFKA_TOPIC_SMOKE}
    volumes:
      - ./data:/app/data
      - ./data_processing:/app/data_processing
      - ./config:/app/config
      - ./app:/app/app
    command: >
      sh -c "sleep 15 &&
           python data_processing/stream_processing/transform_and_save_stream.py"
    restart: unless-stopped

  # ML Training Service - Automatically trains models and generates pickle files
  ml_trainer:
    build:
      context: .
      dockerfile: docker/Dockerfile.ml
    container_name: ml_trainer
    environment:
      ML_AUTO_TRAIN_ON_STARTUP: "true"
      ML_TRAINING_INTERVAL_HOURS: "24"
      PYTHONPATH: /app
    volumes:
      - ./data:/app/data
      - ./ml:/app/ml
      - ./config:/app/config
      - ./app:/app/app
      - ml_models:/app/ml/models
      - ml_logs:/app/logs
    restart: unless-stopped

  # Flask API Backend - Central API layer for predictions and system interaction
  flask_api:
    build:
      context: .
      dockerfile: docker/Dockerfile.api
    container_name: flask_api
    depends_on:
      - ml_trainer
    environment:
      MODEL_PATH: "/app/models/smoke_detection_model.pkl"
      MODEL_RELOAD_INTERVAL: "3600"
      PYTHONPATH: /app
      FLASK_ENV: production
    volumes:
      - ./data:/app/data
      - ./ml:/app/ml
      - ./config:/app/config
      - ./app:/app/app
      - ml_models:/app/ml/models
      - ml_models:/app/models  # Also mount to expected model path
      - ml_logs:/app/logs
    ports:
      - "5000:5000"  # Flask API
    command: >
      sh -c "
        echo 'Starting Flask API with model management...' &&
        python /app/app/start_api.py --wait-for-model --max-wait-minutes 30 --create-placeholder
      "
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Spark Stream Processor - Now calls Flask API instead of loading models directly
  spark_stream_processor:
    build:
      context: .
      dockerfile: docker/Dockerfile.spark
    container_name: spark_stream_processor
    depends_on:
      - kafka
      - zookeeper
      - flask_api
    environment:
      KAFKA_BOOTSTRAP_SERVERS: kafka:${KAFKA_PORT}
      KAFKA_TOPIC_SMOKE: ${KAFKA_TOPIC_SMOKE}
      SPARK_HOME: /opt/spark
      PYSPARK_PYTHON: python3
      PYSPARK_DRIVER_PYTHON: python3
      PYTHONPATH: /app:/opt/spark/python:/opt/spark/python/lib/py4j-********-src.zip
      ML_API_URL: "http://flask_api:5000"
      ENABLE_ML_PREDICTIONS: "true"
    volumes:
      - ./data:/app/data
      - ./data_processing:/app/data_processing
      - ./config:/app/config
      - ./scripts:/app/scripts
      - spark_data:/app/data/spark
      - spark_logs:/app/logs
    ports:
      - "4040:4040"  # Spark UI
      - "4041:4041"  # Additional Spark UI
    command: >
      sh -c "sleep 90 &&
             python3 /app/data_processing/stream_processing/spark_streaming_processor.py --enable-console --ml-api-url http://flask_api:5000"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:4040 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "${POSTGRES_PORT}:${POSTGRES_PORT}"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  airflow:
    build:
      context: .
      dockerfile: docker/Dockerfile.airflow
    container_name: airflow
    depends_on:
      - postgres  
    environment:
      AIRFLOW__CORE__EXECUTOR: ${AIRFLOW__CORE__EXECUTOR}
      AIRFLOW__CORE__FERNET_KEY: ${AIRFLOW__CORE__FERNET_KEY}
      AIRFLOW__CORE__LOAD_EXAMPLES: 'False'
      AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'True'
      AIRFLOW__CORE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres:${POSTGRES_PORT}/airflow
      AIRFLOW__WEBSERVER__WEB_SERVER_PORT: 8080
      AIRFLOW_UID: 50000
      AIRFLOW_GID: 50000
    ports:
      - "8080:8080"
    volumes:
      - ./data_processing/batch_processing/dags:/opt/airflow/dags
      - ./data_processing/batch_processing/tasks:/opt/airflow/tasks
      - ./data:/opt/airflow/data
      - ./app:/opt/airflow/app
      - ./config:/opt/airflow/config
      - ./data_ingestion:/opt/airflow/data_ingestion
    command: >
      bash -c "
      airflow db upgrade &&
      airflow users create --username admin --firstname Admin --lastname User --role Admin --email <EMAIL> --password admin &&
      airflow scheduler &
      airflow webserver
      "
    restart: unless-stopped

#  flask_api:
#    build:
#      context: ./frontend
#      dockerfile: Dockerfile
#    ports:
#      - "${FLASK_PORT}:${FLASK_PORT}"
#    env_file:
#      - .env
#    depends_on:
#      - kafka

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'     # Store data for 30 days
      - '--storage.tsdb.retention.size=5GB'     # Maximum storage size
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    volumes:
      - ./monitoring/grafana-dashboard.json:/etc/grafana/provisioning/dashboards/smoke-detection.json
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  postgres_data:
  spark_data:
  spark_logs:
  ml_models:
  ml_logs:
  prometheus_data:
  grafana_data:
